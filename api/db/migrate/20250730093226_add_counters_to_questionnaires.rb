class AddCountersToQuestionnaires < ActiveRecord::Migration[6.0]
  def change
    add_column :questionnaires, :questionnaire_questions_count, :integer, default: 0, null: false
    add_column :questionnaire_options, :questionnaire_answers_count, :integer, default: 0, null: false
    
    # Reset counter cache for existing records
    reversible do |dir|
      dir.up do
        Questionnaire.reset_column_information
        Questionnaire.find_each do |questionnaire|
          Questionnaire.reset_counters(questionnaire.id, :questionnaire_questions)
        end

        QuestionnaireOption.reset_column_information
        QuestionnaireOption.find_each do |option|
          QuestionnaireOption.reset_counters(option.id, :questionnaire_answers)
        end
      end
    end
  end
end
