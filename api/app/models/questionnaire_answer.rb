# == Schema Information
#
# Table name: questionnaire_answers
#
#  id                        :bigint           not null, primary key
#  answer_value              :string
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  questionnaire_option_id   :bigint
#  questionnaire_question_id :bigint
#  session_id                :string
#  user_id                   :bigint
#
# Indexes
#
#  index_questionnaire_answers_on_questionnaire_option_id    (questionnaire_option_id)
#  index_questionnaire_answers_on_questionnaire_question_id  (questionnaire_question_id)
#  index_questionnaire_answers_on_user_id                    (user_id)
#
class QuestionnaireAnswer < ApplicationRecord
  belongs_to :user, optional: true
  belongs_to :questionnaire_option, counter_cache: true
  belongs_to :questionnaire_question
  validates :answer_value, :session_id, presence: true

  has_many :career_tags, through: :questionnaire_option
end
