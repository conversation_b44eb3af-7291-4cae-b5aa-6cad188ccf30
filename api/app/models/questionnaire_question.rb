# == Schema Information
#
# Table name: questionnaire_questions
#
#  id               :bigint           not null, primary key
#  description      :string
#  question         :string
#  question_data    :jsonb
#  question_type    :integer
#  weight           :integer
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  questionnaire_id :bigint
#
# Indexes
#
#  index_questionnaire_questions_on_questionnaire_id  (questionnaire_id)
#
class QuestionnaireQuestion < ApplicationRecord
  validates :question, :description, :weight, presence: true
  enum question_type: {
    multiChoiceText: 1,
    multiChoiceImage: 2,
    eitherOr: 3,
    selectQuestion: 4,
    gridChoice: 5,
    freeText: 6
  }
  belongs_to :questionnaire
  has_many :questionnaire_options, dependent: :destroy
  has_many :questionnaire_answers

  accepts_nested_attributes_for :questionnaire_options, allow_destroy: true, reject_if: :all_blank

  def options_with_data filters = nil
    questionnaire_options.map do |option|
      option_answers = option.questionnaire_answers
      if filters&.dig(:startDate).present?
        option_answers = option.questionnaire_answers.where("created_at <= ?", filters.dig(:startDate))
      end
      if filters&.dig(:endDate).present?
        option_answers = option.questionnaire_answers.where("created_at >= ?", filters.dig(:endDate))
      end
      {
        option: option,
        count: option_answers.count,
        recent_responses: option_answers.order(created_at: :desc).limit(5),
      }
    end
  end

  def next_question_index
    ordered_questions = questionnaire.questionnaire_questions.order(:weight)
    current_index = ordered_questions.index(self)
    current_index ? current_index + 1 : nil
  end

  def previous_question_index
    ordered_questions = questionnaire.questionnaire_questions.order(:weight)
    current_index = ordered_questions.index(self)
    current_index ? current_index : nil
  end  
end
