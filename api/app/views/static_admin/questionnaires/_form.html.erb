<%= form_with model: [:admin, @questionnaire], url: (admin_questionnaires_path unless @questionnaire.id), local: true, multipart: true do |form| %>
  <% if @questionnaire.errors.any? %>
    <div class="form-errors">
      <ul>
        <% @questionnaire.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="flex-1 min-w-[300px]">
    <%= render AdminFormSectionComponent.new(title: "Details") do %>
      <div class="sm:col-span-2">
        <%= form.label :name %>
        <%= form.text_field :name %>
      </div>

      <div class="col-span-2 xl:col-span-1">
        <%= label_tag "curriculum" %>
        <%= select_tag "curriculum", options_from_collection_for_select(NewLibrary::Curriculum.joins(:years).order('name asc').distinct, 'id', 'name', []), { prompt: 'Select a curriculum...', multiple: false, class: "field field-select", id: "curriculum-select" } %>
      </div>

      <div class="col-span-2 xl:col-span-1">
        <%= label_tag "year" %>
        <%= select_tag "year", options_from_collection_for_select(NewLibrary::Year.order('name asc'), 'id', 'name', []), { prompt: 'Select a year...', multiple: false, class: "field field-select", id: "year-select" } %>
      </div>

      <div class="sm:col-span-2">
        <%= form.label :new_library_unit_id %>
        <%= form.select :new_library_unit_id, options_from_collection_for_select(NewLibrary::Unit.order('name asc'), 'id', 'name', @questionnaire.new_library_unit_id), {}, { prompt: 'Select a unit...', multiple: false, class: "field field-select", id: "unit-select" } %>
      </div>

      <div class="sm:col-span-2 admin-form-info-box">
        <%= form.label :include_demographics_questions do %>
          <div class="border-b !p-4">
            <%= form.check_box :include_demographics_questions %>
            <span class="label">Include Demographics Questions</span>
          </div>
          <div class="!p-4">
            <p>This will include the demographics questions such as Date of birth, career interests, etc..</p>
            <p>Even when enabled, this will not display for anonymous users as this information is stored on a user.</p>
          </div>
        <% end %>
      </div>

      <div class="sm:col-span-2 admin-form-info-box">
        <%= form.label :is_onboarding_questionnaire do %>
          <div class="border-b !p-4">
            <%= form.check_box :is_onboarding_questionnaire %>
            <span class="label">Is Onboarding Questionnaire</span>
          </div>
          <div class="!p-4">
            <p>The onboarding questionnaire is the default questionnaire displayed when a user navigates to the base /questionnaire route. Certain user types (Pupils, Job seekers) are automatically redirected to this route on sign in if they have not yet taken it.</p>
            <p>Changing this to a new questionnaire will reset the taken state for all users, causing them to be prompted to take the new questionnaire.</p>
            <p>If multiple questionnaires are flagged as the onboarding questionnaire, only the most recently created one will be displayed.</p>
            <p>Unpublished questionnaires, even if flagged as onboarding will not be displayed.</p>
          </div>
        <% end %>
      </div>

      <div class="sm:col-span-2 admin-form-info-box">
        <%= form.label :published do %>
          <div class="border-b !p-4">
            <%= form.check_box :published %>
            <span class="label">Published</span>
          </div>
          <div class="!p-4">
            <p>Unpublished questionnaires will not be loaded for users on the front end site.</p>
          </div>
        <% end %>
      </div>
    <% end %>

    <% if !@questionnaire.new_record? %>
      <%= render AdminFormSectionComponent.new(title: "Questions") do %>
        <div>
          <div class="admin-table mb-2">
            <table class="!min-w-0">
              <tbody id="reorderTableBody">
                <% @questionnaire.questionnaire_questions.order('weight asc').each do |question| %>
                  <tr data-id="<%= question.id %>">
                    <td><i class="fas fa-grip-lines"></i></td>
                    <td>
                      <span class="admin-table-primary-col"><%= question.question %></span>
                      <%== question.description %>
                    </td>
                    <td><%= question.question_type.gsub(/([A-Z])/, ' \1').titleize %></td>
                    <%= render TableActionsColumnComponent.new(links: [{ text: "Edit", path: edit_admin_questionnaire_question_path(@questionnaire.id, question.id) }]) %>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          <%= link_to new_admin_questionnaire_question_path(@questionnaire.id), class: "w-full admin-btn" do %>
            <span>Add New Question</span>
          <% end %>
          <%= form.hidden_field :question_order, id: 'question_order' %>
        </div>
      <% end %>
    <% end %>
  </div>

  <div class="admin-form-sticky-submit">
    <%= form.submit 'Save changes', data: { disable_with: 'Processing...' }, class: "admin-btn" %>
  </div>
<% end %>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    const elements = document.querySelectorAll('.field-select');
    const choicesArray = {};
    elements.forEach(element => {
        const choices = new Choices(element, {
            searchEnabled: true, // enables the search feature
            searchResultLimit: 5, // limits the number of search results displayed
            shouldSort: false
        });
        choicesArray[element.id] = choices;
    });

    new Sortable(reorderTableBody, {animation: 150, onEnd: function () { updateQuestionOrder() } });

    var questionOrderField = document.getElementById('question_order');

    function updateQuestionOrder() {
      var reorderTableBody = document.getElementById('reorderTableBody');
      var ids = [];
      var rows = reorderTableBody.querySelectorAll('tr');
      rows.forEach(function (row) {
        var id = row.getAttribute('data-id');
        if (id) {
          ids.push(id);
        }
      });
      questionOrderField.value = ids.join(',');
    }

    document.getElementById("curriculum-select").addEventListener("change", function() {
      const curriculumId = this.value;
      fetchYears(curriculumId);
    });

    document.getElementById("year-select").addEventListener("change", function() {
      const curriculumId = document.getElementById("curriculum-select").value;
      const yearId = this.value;
      fetchUnits(curriculumId, yearId);
    });

    function fetchYears(curriculumId) {
      if (!curriculumId) return;

      fetch(`/admin/library-years/filter?curriculum_id=${curriculumId}`)
        .then(response => response.json())
        .then(data => {
          updateSelect("year-select", data);
          fetchUnits(curriculumId, null);
        });
    }

    function fetchUnits(curriculumId, yearId) {
      const url = new URL("/admin/library-units/filter", window.location.origin);
      if (curriculumId) url.searchParams.append("curriculum_id", curriculumId);
      if (yearId) url.searchParams.append("year_id", yearId);

      fetch(url)
        .then(response => response.json())
        .then(data => {
          updateSelect("unit-select", data);
        });
    }

    function updateSelect(selectId, data) {
      const select = document.getElementById(selectId);

      var options = [];
      data.forEach(item => {
        options.push(new Option(item.name, item.id));
      });

      choicesArray[selectId].clearChoices();
      choicesArray[selectId].setChoices(options);
    }
  });
</script>