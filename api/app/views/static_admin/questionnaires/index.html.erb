<%= render AdminPageBannerComponent.new(title: "Questionnaires", links: [{ text: "+ New", path: new_admin_questionnaire_path }]) %>
<div class="admin-table">
  <div class="admin-table-filters">
    <%= render AdminPageSearchComponent.new(index_path: admin_questionnaires_path) %>
  </div>
  <table>
    <thead>
    <tr>
      <th></th>
      <th></th>
      <th>
        <%= link_to admin_questionnaires_path(sort: 'name', order: flip_order(params[:order])) do %>
          Name
          <%= sort_icon('name', params[:sort], params[:order]) %>
        <% end %>
      </th>
      <th>Onboarding?</th>
      <th>Published</th>
      <th>
        <%= link_to admin_questionnaires_path(sort: 'created_at', order: flip_order(params[:order])) do %>
          Created at
          <%= sort_icon('created_at', params[:sort], params[:order]) %>
        <% end %>
      </th>
      <th></th>
    </tr>
    </thead>
    <tbody>
    <% @questionnaires.each do |questionnaire| %>
      <tr>
        <%= render TableActionsColumnComponent.new(links: [{ text: "Edit", path: edit_admin_questionnaire_path(questionnaire) }]) %>
        <%= render TableActionsColumnComponent.new(links: [{ text: "Preview", path: school_questionnaire_path(questionnaire) }]) %>
        <td><%= questionnaire.name %></td>
        <td><%= checkmark(questionnaire.is_onboarding_questionnaire) %></td>
        <td><%= checkmark(questionnaire.published) %></td>
        <%= render TableDateColumnComponent.new(date: questionnaire.created_at) %>
        <td>
          <div data-controller="modal" class="cursor-pointer">
            <div class="grid" data-action="click->modal#open">
              <button class="admin-btn-small-white">Duplicate</button>
            </div>
            <dialog data-modal-target="dialog" class="max-w-lg p-8">
              <div class="prose">
                <h2>Duplicate questionnaire?</h2>
                <p>This will create a copy of the questionnaire, its questions and their options.</p>
                <p>Answers and statistics will not be duplicated.</p>
                <p>Are you sure you want to proceed?</p>
                <div class="flex justify-end gap-x-2 mt-6">
                  <%= link_to duplicate_admin_questionnaire_path(questionnaire), method: :post  do %>
                    <button class="modal-confirm-btn">Confirm</button>
                  <% end %>
                  <button class="modal-neutral-btn" data-action="click->modal#close">Cancel</button>
                </div>
              </div>
            </dialog>
          </div>
        </td>
      </tr>
    <% end %>
    </tbody>
  </table>
</div>
