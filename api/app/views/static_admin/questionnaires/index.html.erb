<!-- app/views/admin/questionnaires/index.html.erb -->
<% content_for :title, "Questionnaires" %>
<div class="questionnaires-dashboard">
  <!-- Header -->
  <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">All Questionnaires</h1>
      <p class="text-gray-200">Manage questionnaire content</p>
    </div>
    <div class="mt-4 sm:mt-0 flex space-x-3">
      <%= link_to new_admin_questionnaire_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-de-brand hover:bg-de-brand-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" do %>
        <i class="fa-duotone fa-solid fa-plus mr-2"></i>
        New Questionnaire
      <% end %>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex gap-4 flex-col">
    <!-- Filters Panel -->
    <div class="flex-shrink-0">
      <div class="bg-white shadow rounded-lg sticky top-4">
        <div class="px-4 py-5 border-b border-gray-200">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Filter Questionnaires</h3>
        </div>
        <%= form_with(url: admin_questionnaires_path, method: :get, local: true, class: "px-4 py-5 flex gap-4 flex-col xl:flex-row") do %>
          <%= hidden_field_tag :order, params[:order] %>
          <%= hidden_field_tag :sort, params[:sort] %>
          <div class="flex flex-col md:flex-row gap-4 flex-1">
            <div class="flex-1">
              <%= label_tag :query, 'Search by Name', class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1 relative rounded-md shadow-sm">
                <%= text_field_tag :query, params[:query], placeholder: "Search questionnaires...", class: "focus:ring-de-brand focus:border-de-brand block w-full pr-10 sm:text-sm border-gray-300 rounded-md" %>
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <i class="fa-solid fa-search h-5 w-5 text-gray-400"></i>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-end">
            <%= submit_tag "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-de-brand hover:bg-de-brand-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" %>
            <%= link_to admin_questionnaires_path, class: "ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" do %>
              Clear
            <% end %>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Questionnaires Table -->
    <div class="flex-1">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6 flex justify-between items-center">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Questionnaire List</h3>
          <div class="text-sm text-gray-500">
            <%= render AdminPaginateComponent.new(items: @questionnaires) %>
          </div>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="admin-table-header">
                  <%= sortable_link('name', 'Name', ->(params) { admin_questionnaires_path(params) }) %>
                </th>
                <th scope="col" class="admin-table-header">Status</th>
                <th scope="col" class="admin-table-header">Type</th>
                <th scope="col" class="admin-table-header">
                  <%= sortable_link('created_at', 'Created At', ->(params) { admin_questionnaires_path(params) }) %>
                </th>
                <th scope="col" class="admin-table-header">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% if @questionnaires.empty? %>
                <tr>
                  <td colspan="5" class="px-6 py-8 text-center text-sm text-gray-500">
                    No questionnaires found matching your criteria.
                    <div class="mt-2">
                      <%= link_to new_admin_questionnaire_path, class: "text-de-brand hover:text-de-brand-dark font-medium" do %>
                        Create your first questionnaire
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% else %>
                <% @questionnaires.each do |questionnaire| %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4">
                      <div class="flex items-center">
                        <div>
                          <div class="text-sm font-medium text-gray-900">
                            <%= link_to questionnaire.name, edit_admin_questionnaire_path(questionnaire), class: "hover:text-de-brand" %>
                          </div>
                          <div class="text-sm text-gray-500">
                            <%= pluralize(questionnaire.questionnaire_questions_count, 'question') %>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <% if questionnaire.published %>
                        <span class="admin-badge bg-green-100 text-green-800">
                          ✅ Published
                        </span>
                      <% else %>
                        <span class="admin-badge bg-yellow-100 text-yellow-800">
                          📝 Draft
                        </span>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <% if questionnaire.is_onboarding_questionnaire %>
                        <span class="admin-badge bg-blue-100 text-blue-800">
                          🚀 Onboarding
                        </span>
                      <% else %>
                        <span class="admin-badge bg-gray-100 text-gray-800">
                          📋 Standard
                        </span>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                      <%= questionnaire.created_at.strftime("%b %d, %Y") %>
                      <div class="text-xs text-gray-400">
                        <%= time_ago_in_words(questionnaire.created_at) %> ago
                      </div>
                    </td>
                    <td class="px-6 py-4 text-sm font-medium">
                      <div class="flex space-x-2">
                        <%= link_to edit_admin_questionnaire_path(questionnaire), class: "btn btn-sm btn-flat-cyan" do %>
                          Edit
                        <% end %>
                        <%= link_to statistics_admin_questionnaire_path(questionnaire), class: "btn btn-sm btn-flat-white" do %>
                          <i class="fa-duotone fa-chart-bar mr-2"></i>
                          Stats
                        <% end %>
                        <div data-controller="modal" class="cursor-pointer">
                          <div class="grid" data-action="click->modal#open">
                            <button class="btn btn-sm btn-flat-white">
                              <i class="fa-duotone fa-copy mr-2"></i>
                              Duplicate
                            </button>
                          </div>
                          <dialog data-modal-target="dialog" class="max-w-lg p-8">
                            <div class="text-black">
                              <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Duplicate Questionnaire</h3>
                              <p class="text-sm text-gray-500 mb-4">Are you sure you want to duplicate this questionnaire? This will create a copy with all questions.</p>
                              <div class="flex justify-end space-x-3">
                                <button type="button" class="admin-btn-small-white" data-action="click->modal#close">Cancel</button>
                                <%= form_with url: duplicate_admin_questionnaire_path(questionnaire), method: :post, local: true, class: "inline" do |f| %>
                                  <%= f.submit "Duplicate", class: "admin-btn-small" %>
                                <% end %>
                              </div>
                            </div>
                          </dialog>
                        </div>
                      </div>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
