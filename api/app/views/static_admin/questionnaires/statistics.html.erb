<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<% content_for :title, "Statistics | #{@questionnaire.name} | Questionnaire" %>
<%= render AdminPageBannerComponent.new(
  title: "Edit #{@questionnaire.name}",
  back_to: { path: admin_questionnaires_path, text: "All questionnaires" }
) %>
<div class="text-white">
  <%= render "tabs", active_tab: :statistics %>
</div>

<div class="bg-gray-50 min-h-screen p-8">
  <!-- Filters Section -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-8 overflow-hidden">
    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Filter Statistics</h3>
      <p class="text-sm text-gray-600 mt-1">Adjust date ranges to analyze response patterns over specific time periods.</p>
    </div>
    <div class="p-6">
      <%= form_with(url: statistics_admin_questionnaire_path(@questionnaire), method: :get, local: true, class: "space-y-4") do %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <%= label_tag :startDate, 'Date From', class: "block text-sm font-medium text-gray-700" %>
            <%= date_field_tag :startDate, params[:startDate], class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
          </div>
          <div>
            <%= label_tag :endDate, 'Date To', class: "block text-sm font-medium text-gray-700" %>
            <%= date_field_tag :endDate, params[:endDate], class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
        <div class="flex space-x-3">
          <%= submit_tag "Apply Filters", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <%= link_to statistics_admin_questionnaire_path(@questionnaire), class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            Reset Filters
          <% end %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Statistics Content -->
  <div class="space-y-8">
    <% @statistics.each_with_index do |stat, i| %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <div class="flex justify-between items-start">
            <div>
              <h3 class="text-lg font-semibold text-gray-900"><%= stat[:question].question %></h3>
              <p class="text-sm text-gray-600 mt-1">
                Question Type: <span class="font-medium"><%= stat[:question].question_type.gsub(/([A-Z])/, ' \1').titleize %></span>
                • Total Responses: <span class="font-medium"><%= stat[:options_with_data].sum { |opt| opt[:count] } %></span>
              </p>
            </div>
            <%= link_to statistics_admin_questionnaire_question_path(@questionnaire, stat[:question]), class: "inline-flex items-center px-3 py-2 border border-indigo-300 shadow-sm text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-50 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
              <i class="fa-solid fa-chart-line mr-2"></i>
              View Demographics
            <% end %>
          </div>
        </div>

        <div class="p-6">
          <% if stat[:question][:question_type] != 'freeText' %>
            <!-- Chart and Table Layout -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Chart Section -->
              <div>
                <h4 class="text-md font-semibold text-gray-900 mb-4">Response Distribution</h4>
                <div class="h-80 relative">
                  <canvas id="chart-<%= i %>"></canvas>
                </div>
              </div>

              <!-- Top Answers Table -->
              <div>
                <h4 class="text-md font-semibold text-gray-900 mb-4">Top 5 Responses</h4>
                <div class="bg-gray-50 rounded-lg overflow-hidden">
                  <table class="w-full">
                    <thead class="bg-gray-100">
                      <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Answer</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">%</th>
                      </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                      <% total_responses = stat[:options_with_data].sum { |opt| opt[:count] } %>
                      <% stat[:options_with_data].sort_by { |k| k[:count] }.reverse.first(5).each do |top| %>
                        <tr class="hover:bg-gray-50">
                          <td class="px-4 py-3 text-sm font-medium text-gray-900">
                            <%= stat[:question][:question_type] == 'selectQuestion' ? top[:option][:option_data]["label"] : top[:option][:option_data]["value"] %>
                          </td>
                          <td class="px-4 py-3 text-sm text-gray-600"><%= top[:count] %></td>
                          <td class="px-4 py-3 text-sm text-gray-600">
                            <%= total_responses > 0 ? "#{((top[:count].to_f / total_responses) * 100).round(1)}%" : "0%" %>
                          </td>
                        </tr>
                      <% end %>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          <% else %>
            <!-- Free Text Responses -->
            <div>
              <h4 class="text-md font-semibold text-gray-900 mb-4">Recent Responses</h4>
              <div class="space-y-3">
                <% stat[:options_with_data][0][:recent_responses].first(5).each_with_index do |response, idx| %>
                  <div class="bg-gray-50 rounded-lg p-4 border-l-4 border-indigo-500">
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-800 text-xs font-medium">
                          <%= idx + 1 %>
                        </span>
                      </div>
                      <div class="ml-3 flex-1">
                        <p class="text-sm text-gray-900"><%= response[:answer_value] %></p>
                        <p class="text-xs text-gray-500 mt-1">
                          Submitted <%= time_ago_in_words(response[:created_at]) %> ago
                        </p>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>

    <% if @statistics.empty? %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
        <div class="text-gray-500">
          <i class="fa-solid fa-chart-bar text-4xl mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Statistics Available</h3>
          <p class="text-sm text-gray-500 mb-4">This questionnaire hasn't received any responses yet.</p>
        </div>
      </div>
    <% end %>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const stats = <%= @statistics.to_json.html_safe %>;

    // Color palette matching campaigns impressions
    const colorPalette = [
        '#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444',
        '#6366F1', '#EC4899', '#14B8A6', '#F97316', '#84CC16',
        '#6B7280', '#8B5A2B', '#7C3AED', '#059669', '#DC2626'
    ];

    const chartColors = {
        primary: '#4F46E5',
        secondary: '#7C3AED',
        success: '#10B981',
        warning: '#F59E0B',
        danger: '#EF4444'
    };

    const charts = document.querySelectorAll('canvas');
    charts.forEach(chart => {
        const index = chart.id.split('-')[1];
        const questionType = stats[index].question.question_type;
        const optionsData = stats[index].options_with_data;

        const sortedOptionsData = optionsData.sort((a, b) => b.count - a.count);

        const { labels, data } = sortedOptionsData.reduce((acc, { option, count }) => {
            acc.labels.push(questionType === "selectQuestion" ? option.option_data.label : option.option_data.value);
            acc.data.push(count);
            return acc;
        }, { labels: [], data: [] });

        const ctx = document.getElementById(chart.id);

        // Determine chart type based on data
        const chartType = data.length <= 6 ? 'doughnut' : 'bar';

        new Chart(ctx, {
            type: chartType,
            data: {
                labels,
                datasets: [{
                    label: 'Responses',
                    data,
                    backgroundColor: chartType === 'doughnut' ? colorPalette : chartColors.primary,
                    borderWidth: chartType === 'doughnut' ? 0 : 1,
                    borderRadius: chartType === 'bar' ? 6 : 0,
                    borderSkipped: chartType === 'bar' ? false : undefined,
                    hoverOffset: chartType === 'doughnut' ? 8 : undefined
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: chartType === 'doughnut',
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: { size: 12 },
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map((label, i) => {
                                        const value = data.datasets[0].data[i];
                                        const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                        const percentage = ((value / total) * 100).toFixed(1);
                                        return {
                                            text: `${label} (${percentage}%)`,
                                            fillStyle: data.datasets[0].backgroundColor[i],
                                            strokeStyle: data.datasets[0].backgroundColor[i],
                                            lineWidth: 0,
                                            pointStyle: 'circle',
                                            hidden: false,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                },
                scales: chartType === 'bar' ? {
                    y: {
                        beginAtZero: true,
                        grid: { color: '#f1f5f9' },
                        ticks: {
                            font: { size: 11 }
                        }
                    },
                    x: {
                        grid: { display: false },
                        ticks: {
                            font: { size: 11 },
                            maxRotation: 45
                        }
                    }
                } : {}
            }
        });
    });
});
</script>