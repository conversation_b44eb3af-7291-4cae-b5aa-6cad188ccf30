<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="flex-1 min-w-[300px]">
    <%= render AdminPageBannerComponent.new(title: "Statistics", back_to: { path: edit_admin_questionnaire_path(@questionnaire), text: "Back to Questionnaire" }) %>
    <div class="mb-4">
      <%= form_with(url: statistics_admin_questionnaire_path(@questionnaire), method: :get, local: true) do %>
        <div class="grid gap-x-4 w-full">
          <div class="admin-form-section bg-white rounded p-4 grid grid-cols-2 gap-4">
            <h3 class="mb-2 col-span-2">Filter all questions statistics</h3>
            <div>
              <%= label_tag 'Date From' %>
              <%= date_field_tag :startDate, params[:startDate] %>
            </div>
            <div>
              <%= label_tag 'Date To' %>
              <%= date_field_tag :endDate, params[:endDate] %>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-x-4">
            <button class="mt-2 w-full admin-btn">Search</button>
            <%= link_to statistics_admin_questionnaire_path(@questionnaire) do %>
                <button type="button" class="mt-2 w-full admin-btn">Reset Filters</button>
            <% end %>
        </div>
      <% end %>
    </div>

    <div class="p-8 bg-white rounded grid gap-y-4">
        <% @statistics.each_with_index do |stat, i| %>
            <div class="admin-form-info-box">
                <div class="flex justify-between border-b !px-4 !py-1">
                    <h2 class="!border-0"><%= stat[:question].question %></h2>
                    <%= link_to "View Demographics", statistics_admin_questionnaire_question_path(@questionnaire, stat[:question]), class: "admin-btn my-auto" %>
                </div>
                <div class="!px-2">
                    <% if stat[:question][:question_type] != 'freeText' %>
                        <canvas id="chart-<%= i %>" class="mb-4"></canvas>
                        <p class="font-semibold text-lg">Top 5 given answers</p>
                        <div class="!p-2">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b">
                                        <th class="text-start">Answer</th>
                                        <th class="text-start">Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% stat[:options_with_data].sort_by { |k| k[:count] }.reverse.first(5).each do |top| %>
                                        <tr class="border-b">
                                            <td><%= stat[:question][:question_type] == 'selectQuestion' ? top[:option][:option_data]["label"] : top[:option][:option_data]["value"] %></td>
                                            <td><%= top[:count] %></td>
                                        </tr>
                                    <% end %>
                                </tbody>
                            </table>
                        </div>
                    <% else %>
                        <p class="font-semibold text-lg">5 most recent responses</p>
                        <% stat[:options_with_data][0][:recent_responses].first(5).each_with_index do |top, i| %>
                            <p class="py-2 border-b border-dashed">
                                <%= i + 1 %>. <%= top[:answer_value] %>
                            </p>
                        <% end %>
                    <% end %>
                </div>
            </div>
        <% end %>
    </div>
</div>

<script>
    const stats = <%= @statistics.to_json.html_safe %>;
    const charts = document.querySelectorAll('canvas');
    charts.forEach(chart => {
        const index = chart.id.split('-')[1];
        const questionType = stats[index].question.question_type;
        const optionsData = stats[index].options_with_data;

        const sortedOptionsData = optionsData.sort((a, b) => b.count - a.count);

        const { labels, data } = sortedOptionsData.reduce((acc, { option, count }) => {
            acc.labels.push(questionType === "selectQuestion" ? option.option_data.label : option.option_data.value);
            acc.data.push(count);
            return acc;
        }, { labels: [], data: [] });

        const ctx = document.getElementById(chart.id);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets: [{
                    label: 'Count',
                    data,
                    borderWidth: 1,
                    backgroundColor: [
                        "#4E89CD",
                        "#7468B9",
                        "#54A763",
                        "#67D7D1",
                        "#261177",
                        "#DB8A90",
                        "#7A7E64",
                        "#4A8F21",
                        "#B42CB0",
                        "#E1D49E",
                        "#2234B3",
                        "#F3BA4A",
                        "#F9075A",
                        "#07DFB0",
                        "#8A9F8D",
                        "#661DE8",
                        "#096FD8",
                        "#C623AA",
                        "#3DBDA5",
                        "#C6F573",
                        "#1E2A48",
                    ],
                }]
            },
            options: {
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    })
</script>