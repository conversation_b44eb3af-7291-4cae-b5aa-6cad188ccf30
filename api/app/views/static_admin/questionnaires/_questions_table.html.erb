<%# Include Sortable.js from CDN directly in the view %>
<%= javascript_include_tag 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js' %>

<div
    id="questions-container"
    data-questionnaire-id="<%= @questionnaire.id %>"
  >
  <div class="flex justify-between items-center gap-2 p-4 border-b sticky top-0 bg-white rounded-t-lg z-10 flex-wrap">
    <div class="flex gap-4 items-center flex-wrap">
      <h3 class="text-lg font-medium text-gray-900">Questions</h3>
      <span class="text-sm text-gray-500">
        <%= pluralize(@questionnaire.questionnaire_questions.count, 'question') %>
      </span>
    </div>
    <%# New Question Button (triggers drawer) %>
    <%= render(DrawerComponent.new(title: "Add New Question", id: "new-question-drawer", position: "right")) do |drawer| %>
      <% drawer.with_trigger do %>
        <button class="btn btn-base btn-cyan">
          <i class="fa-solid fa-plus mr-2"></i>Add Question
        </button>
      <% end %>
      <%# New Question Form %>
      <%- question = QuestionnaireQuestion.new %>
      <%= form_with(
            model: question,
            url: new_question_path.call(@questionnaire),
            local: true,
            scope: :questionnaire_question,
            id: "new-question-form"
          ) do |f| %>
        <div class="space-y-4">
          <div class="field-base">
            <%= f.label :question_type, class: "block text-sm font-medium text-gray-700" %>
            <%= f.select :question_type,
                  options_for_select([
                    ['Multi Choice Text', 'multiChoiceText'],
                    ['Multi Choice Image', 'multiChoiceImage'],
                    ['Either Or', 'eitherOr'],
                    ['Select Question', 'selectQuestion'],
                    ['Grid Choice', 'gridChoice'],
                    ['Free Text', 'freeText']
                  ]),
                  {},
                  { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 question-type-select", required: true }
                %>
          </div>
          
          <div class="field-base">
            <%= f.label :question, class: "block text-sm font-medium text-gray-700", required: true %>
            <%= f.text_field :question, required: true, placeholder: "Enter question text...", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
          </div>

          <div class="field-base">
            <%= f.label :description, class: "block text-sm font-medium text-gray-700", required: true %>
            <%= f.text_area :description, required: true, placeholder: "Enter question description...", rows: 3, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
          </div>

          <div class="field-base">
            <%= f.label :weight, "Order", class: "block text-sm font-medium text-gray-700" %>
            <%= f.number_field :weight, value: (@questionnaire.questionnaire_questions.maximum(:weight) || 0) + 1, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button type="button" class="btn btn-flat-white" data-action="drawer#close">
              Cancel
            </button>
            <%= f.submit "Add Question", class: "btn btn-cyan" %>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>

  <% if @questionnaire.questionnaire_questions.any? %>
    <div class="bg-white rounded-b-lg shadow-sm">
      <table class="min-w-full">
        <thead class="bg-gray-50 border-b border-gray-200">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8">
              <!-- Drag handle column -->
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Question
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
              Type
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
              Order
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
              Actions
            </th>
          </tr>
        </thead>
        <tbody id="questions-table-body" class="bg-white divide-y divide-gray-200">
          <% @questionnaire.questionnaire_questions.order(:weight).each do |question| %>
            <tr data-question-id="<%= question.id %>" class="hover:bg-gray-50 transition-colors duration-150">
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="handle cursor-move text-gray-400 hover:text-gray-600">
                  <i class="fa-solid fa-grip-vertical text-lg"></i>
                </div>
              </td>
              <td class="px-4 py-4">
                <div class="text-sm font-medium text-gray-900 line-clamp-2">
                  <%= question.question %>
                </div>
                <div class="text-sm text-gray-500 line-clamp-2 mt-1">
                  <%== question.description %>
                </div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= question.question_type.gsub(/([A-Z])/, ' \1').titleize %>
                </span>
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                <%= question.weight %>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-3">
                  <%# Edit Question Drawer %>
                  <%= render(DrawerComponent.new(title: "Edit Question", id: "edit-question-#{question.id}-drawer", position: "right")) do |drawer| %>
                    <% drawer.with_trigger do %>
                      <button class="inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fa-solid fa-pen-to-square mr-1"></i>Edit
                      </button>
                    <% end %>
                    <%# Edit Question Form %>
                    <%= form_with(
                          model: question,
                          url: edit_question_path.call(@questionnaire, question),
                          method: :patch,
                          local: true,
                          scope: :questionnaire_question,
                          id: "edit-question-form-#{question.id}"
                        ) do |f| %>
                      <div class="space-y-4">
                        <div class="field-base">
                          <%= f.label :question_type, class: "block text-sm font-medium text-gray-700" %>
                          <%= f.select :question_type,
                                options_for_select([
                                  ['Multi Choice Text', 'multiChoiceText'],
                                  ['Multi Choice Image', 'multiChoiceImage'],
                                  ['Either Or', 'eitherOr'],
                                  ['Select Question', 'selectQuestion'],
                                  ['Grid Choice', 'gridChoice'],
                                  ['Free Text', 'freeText']
                                ], question.question_type),
                                {},
                                { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 question-type-select", required: true }
                              %>
                        </div>
                        
                        <div class="field-base">
                          <%= f.label :question, class: "block text-sm font-medium text-gray-700", required: true %>
                          <%= f.text_field :question, required: true, placeholder: "Enter question text...", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
                        </div>

                        <div class="field-base">
                          <%= f.label :description, class: "block text-sm font-medium text-gray-700", required: true %>
                          <%= f.text_area :description, required: true, placeholder: "Enter question description...", rows: 3, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
                        </div>

                        <div class="field-base">
                          <%= f.label :weight, "Order", class: "block text-sm font-medium text-gray-700" %>
                          <%= f.number_field :weight, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" %>
                        </div>

                        <div class="flex justify-end space-x-3 pt-4">
                          <button type="button" class="btn btn-flat-white" data-action="drawer#close">
                            Cancel
                          </button>
                          <%= f.submit "Update Question", class: "btn btn-cyan" %>
                        </div>
                      </div>
                    <% end %>
                  <% end %>

                  <%# Delete Question %>
                  <%= link_to delete_question_path.call(@questionnaire, question), 
                      method: :delete, 
                      data: { 
                        confirm: 'Are you sure you want to delete this question? This action cannot be undone.' 
                      },
                      class: "inline-flex items-center px-2.5 py-1.5 border border-red-300 text-sm font-medium rounded text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" do %>
                    <i class="fa-solid fa-trash mr-1"></i>Delete
                  <% end %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  <% else %>
    <div class="bg-white rounded-b-lg p-8 text-center">
      <div class="text-gray-500">
        <i class="fa-solid fa-question-circle text-4xl mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No questions yet</h3>
        <p class="text-sm text-gray-500 mb-4">Get started by adding your first question to this questionnaire.</p>
        <button class="btn btn-cyan" data-action="drawer#open" data-drawer-target="new-question-drawer">
          <i class="fa-solid fa-plus mr-2"></i>Add First Question
        </button>
      </div>
    </div>
  <% end %>
</div>

<style>
  /* Sortable styles */
  .sortable-chosen {
    opacity: 0.5;
  }

  .sortable-fallback {
    opacity: 0.8;
    background: #f3f4f6;
    border: 2px dashed #d1d5db;
  }

  /* Prevent default browser drag behavior on table rows */
  #questions-table-body tr {
    -webkit-user-drag: none;
    user-drag: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Additional fixes for Chrome's globe icon */
  .sortable-drag {
    opacity: 0 !important;
  }

  /* Line clamp for truncating text */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>

<script>
  // Initialize Sortable when the DOM is fully loaded
  document.addEventListener('DOMContentLoaded', function() {
    initSortable();
    preventDefaultDrag();

    const urlParams = new URLSearchParams(window.location.search);
    const questionId = urlParams.get('questionnaire_question_id');
    if (questionId) {
      const editDrawer = document.getElementById(`edit-question-${questionId}-drawer`);
      if (editDrawer) {
        // Small delay to ensure drawer is ready
        setTimeout(() => {
          const triggerButton = editDrawer.previousElementSibling;
          if (triggerButton) {
            triggerButton.click();
          }
        }, 100);
      }
    }
  });

  function initSortable() {
    const questionsTableBody = document.getElementById('questions-table-body');
    const questionsContainer = document.getElementById('questions-container');

    if (!questionsTableBody || !questionsContainer) {
      return;
    }

    if (typeof Sortable === 'undefined') {
      console.error('Sortable.js is not loaded');
      return;
    }

    // Remove any existing sortable instance to avoid duplicates
    if (questionsTableBody.sortableInstance) {
      questionsTableBody.sortableInstance.destroy();
    }

    // Initialize Sortable
    questionsTableBody.sortableInstance = Sortable.create(questionsTableBody, {
      animation: 150,
      handle: '.handle',
      ghostClass: 'bg-blue-100',
      dragClass: 'sortable-drag',
      chosenClass: 'sortable-chosen',
      forceFallback: true,
      fallbackClass: 'sortable-fallback',

      onChoose: function(evt) {
        evt.item.classList.add('dragging');
      },

      onUnchoose: function(evt) {
        evt.item.classList.remove('dragging');
      },

      onEnd: function(evt) {
        evt.item.classList.remove('dragging');

        // Get the new order of question IDs
        const questionIds = Array.from(questionsTableBody.children).map(row => {
          return row.getAttribute('data-question-id');
        });

        // Send the reorder request
        const questionnaireId = questionsContainer.getAttribute('data-questionnaire-id');
        const formData = new FormData();
        formData.append('question_order', questionIds.join(','));
        formData.append('_method', 'PATCH');

        fetch(`/admin/questionnaires/${questionnaireId}`, {
          method: 'POST',
          body: formData,
          headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        }).then(response => {
          if (!response.ok) {
            console.error('Failed to reorder questions');
            // Optionally reload the page or show an error message
          }
        }).catch(error => {
          console.error('Error reordering questions:', error);
        });
      }
    });
  }

  function preventDefaultDrag() {
    const questionsTableBody = document.getElementById('questions-table-body');
    if (!questionsTableBody) return;

    questionsTableBody.addEventListener('dragstart', function(e) {
      if (!e.target.closest('.handle')) {
        e.preventDefault();
      }
    });
  }
</script>
