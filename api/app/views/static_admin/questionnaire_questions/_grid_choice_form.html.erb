<%= form_with model: @question, url: admin_questionnaire_question_path(@questionnaire, @question), local: true, multipart: true do |form| %>
  <% if @question.errors.any? %>
    <div class="form-errors">
      <ul>
        <% @question.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="flex-1 min-w-[300px]">
    <%= render AdminFormSectionComponent.new(title: "Details") do %>
      <div class="admin-form-info-box">
        <h2>Grid Choice Question</h2>
        <div>
          <p>Options are automatically batched into groups of up to 12 items.</p>
          <p>For each item over the 12 item limit for a group, a new page (group) will be added automatically, allowing users to continue to the next page until there are no more items.</p>
        </div>
      </div>

      <div class="admin-form-info-box">
        <div>
          <p>Option tags allow assigning of tags to users. When a user selects an option as their answer, at the end of the questionnaire all answers are checked for tags, the users interest tags are then updated based on the answers.</p>
          <p>To add new options, click the "Add Option" button at the bottom of the page.</p>
          <p>To remove options, click the bin icon on the option you want to remove.</p>
          <p>Options will not be removed until the form is submitted.</p>
          <p>You can undo a deletion before saving by clicking the bin button again (displayed as a refresh button when items are flagged for deletion).</p>
          <p>All changes (including options) are saved when submitting the main form via the"Submit" button.</p>
        </div>
      </div>

      <div class="sm:col-span-2">
        <%= form.label :question %>
        <%= form.text_field :question %>
      </div>

      <div class="sm:col-span-2">
        <%= form.label :description %>
        <%= form.text_area :description, class: 'ckeditor' %>
      </div>
    <% end %>

    <%= render AdminFormSectionComponent.new(title: "Options") do %>
      <div id="options-list" class="grid gap-y-4">
        <%= form.fields_for :questionnaire_options, @question.questionnaire_options.sort_by(&:weight) do |option_form| %>
          <div class="admin-form-option-box sm:col-span-2 flex gap-x-2" data-option-box>
            <%= option_form.hidden_field :_destroy %>
            <%= option_form.hidden_field :weight %>

            <div class="grow">
              <div class="sm:col-span-2">
                <%= option_form.label :value, "Text" %>
                <%= option_form.text_field :value, value: option_form.object.option_data['value'], name: "questionnaire_question[questionnaire_options_attributes][#{option_form.index}][option_data][value]" %>
              </div>

              <div class="sm:col-span-2">
                <%= option_form.label :fileboy_image, "Background Image" %>
                <%- if option_form.object.option_data['fileboyId'] %>
                  <div class="p-2">
                    <div class="ml-auto">
                      <div class="overflow-hidden border w-[200px] h-[200px]">
                        <img src="https://www.developingexperts.com/file-cdn/images/get/<%= option_form.object.option_data['fileboyId'] %>?transform=resize:200x200"/>
                      </div>
                    </div>
                  </div>
                <% else %>
                  <div class="hidden p-2" id="img-preview">
                    <div class="overflow-hidden border w-[200px] h-[200px]">
                      <img class="object-cover" id="img"/>
                    </div>
                  </div>
                <% end %>
                <%= file_field_tag :fileboy_image, name: "questionnaire_question[questionnaire_options_attributes][#{option_form.index}][fileboy_image_id]" %>
              </div>

              <div class="sm:col-span-2">
                <%= option_form.label :career_tag_ids, "Tags" %>
                <%= select_tag "questionnaire_question[questionnaire_options_attributes][#{option_form.index}][career_tag_ids]", options_for_select(@career_tags, option_form.object.career_tag_ids), { multiple: true, class: 'field-select' } %>
              </div>
            </div>

            <div class="my-auto">
              <button type="button" class="remove-option rounded-full bg-red-500 text-white h-10 w-10" data-remove-option>
                <i class="fa-solid fa-trash"></i>
              </button>
            </div>
          </div>
        <% end %>
      </div>

      <div class="sm:col-span-2">
        <button type="button" id="add-option" class="admin-btn">Add Option</button>
      </div>
    <% end %>
  </div>

  <div class="admin-form-sticky-submit">
    <%= form.submit 'Save changes', data: { disable_with: 'Processing...' }, class: "admin-btn" %>
  </div>
<% end %>

<script>
  const elements = document.querySelectorAll('.field-select');
  elements.forEach(element => {
      const choices = new Choices(element, {
          removeItemButton: true, // enables the removal of selected items
            searchEnabled: true, // enables the search feature
            searchResultLimit: 5, // limits the number of search results displayed
            position: 'top',
            shouldSort: false,
      });
  })

  document.addEventListener("DOMContentLoaded", () => {
    const optionsList = document.getElementById("options-list");
    const addOptionButton = document.getElementById("add-option");

    document.querySelectorAll("[data-remove-option]").forEach((button) => {
      button.addEventListener("click", (event) => {
        const optionBox = event.target.closest("[data-option-box]");
        const destroyField = optionBox.querySelector("input[name*='_destroy']");
        if (destroyField && destroyField.value !== "1") {
          destroyField.value = "1";
          optionBox.style.opacity = 0.5;
        } else if (destroyField && destroyField.value === "1") {
          destroyField.value = "0";
          optionBox.style.opacity = 1;
        }
      });
    });

    let optionIndex = optionsList.querySelectorAll("[data-option-box]").length;
    let newOptionIndex = 1;

    addOptionButton.addEventListener("click", (event) => {
      event.preventDefault();

      const newOptionHtml = `
        <div id="new-option-${newOptionIndex}" class="admin-form-option-box sm:col-span-2 flex" data-option-box>
          <input type="hidden" name="questionnaire_question[questionnaire_options_attributes][${optionIndex}][_destroy]" value="false">
          <input type="hidden" name="questionnaire_question[questionnaire_options_attributes][${optionIndex}][weight]" value="${optionIndex}">
          
          <div class="grow">
            <div class="sm:col-span-2">
              <label for="questionnaire_question_questionnaire_options_attributes_${optionIndex}_value">Text</label>
              <input type="text" name="questionnaire_question[questionnaire_options_attributes][${optionIndex}][option_data][value]" id="questionnaire_question_questionnaire_options_attributes_${optionIndex}_value">
            </div>
            
            <div class="sm:col-span-2">
              <label for="fileboy_image">Background Image</label>
              <input type="file" name="questionnaire_question[questionnaire_options_attributes][${optionIndex}][fileboy_image_id]">
            </div>

            <div class="sm:col-span-2">
              <label for="questionnaire_question[questionnaire_options_attributes][${optionIndex}][career_tag_ids]">Career Tags</label>
              <select name="questionnaire_question[questionnaire_options_attributes][${optionIndex}][career_tag_ids][]" multiple class="field-select">
                ${getCareerTagOptionsHtml()}
              </select>
            </div>
          </div>

          <div class="my-auto">
            <button id="remove-new-${newOptionIndex}" type="button" class="remove-option rounded-full bg-red-500 text-white h-10 w-10" data-remove-option>
              <i class="fa-solid fa-trash"></i>
            </button>
          </div>
        </div>
      `;

      optionsList.insertAdjacentHTML("beforeend", newOptionHtml);

      const currentOptionIndex = newOptionIndex;

      document.getElementById(`remove-new-${currentOptionIndex}`).addEventListener("click", (event) => {
        document.getElementById(`new-option-${currentOptionIndex}`).remove();
      });

      optionIndex++;
      newOptionIndex++;

      const lastSelect = document.querySelector('.field-select:last-child');
      const choices = new Choices(lastSelect, {
          removeItemButton: true, // enables the removal of selected items
          searchEnabled: true, // enables the search feature
          searchResultLimit: 5, // limits the number of search results displayed
          position: 'top',
          shouldSort: false,
      });
    });

    function getCareerTagOptionsHtml() {
      return `<%= @career_tags.map { |tag| "<option value='#{tag[1]}'>#{tag[0]}</option>" }.join('').html_safe %>`;
    }
  });
</script>