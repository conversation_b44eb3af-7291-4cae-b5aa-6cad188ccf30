<%= form_with model: @question, url: admin_questionnaire_question_path(@questionnaire, @question), local: true, multipart: true do |form| %>
  <% if @question.errors.any? %>
    <div class="form-errors">
      <ul>
        <% @question.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="flex-1 min-w-[300px]">
    <%= render AdminFormSectionComponent.new(title: "Details") do %>
      <div class="admin-form-info-box">
        <h2>Free Text Question</h2>
        <div>
          <p>Free text questions do not support options</p>
        </div>
      </div>

      <div class="sm:col-span-2">
        <%= form.label :question %>
        <%= form.text_field :question %>
      </div>

      <div class="sm:col-span-2">
        <%= form.label :description %>
        <%= form.text_area :description, class: 'ckeditor' %>
      </div>

      <p>Free text questions do not support options</p>
    <% end %>
  </div>

  <div class="admin-form-sticky-submit">
    <%= form.submit 'Save changes', data: { disable_with: 'Processing...' }, class: "admin-btn" %>
  </div>
<% end %>