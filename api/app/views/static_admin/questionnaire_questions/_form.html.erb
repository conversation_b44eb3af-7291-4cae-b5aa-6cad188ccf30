<%= form_with model: @question, url: @question.new_record? ? admin_questionnaire_questions_path(@questionnaire) : admin_questionnaire_question_path(@questionnaire, @question), local: true, multipart: true do |form| %>
  <% if @question.errors.any? %>
    <div class="form-errors">
      <ul>
        <% @question.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="flex-1 min-w-[300px]">
    <%= render AdminFormSectionComponent.new(title: "Details") do %>
      <div class="sm:col-span-2">
        <%= form.label :question_type %>
        <%= form.select :question_type, options_for_select([['Multi Choice', 'multiChoiceImage'], ['Either Or', 'eitherOr'], ['Select Question', 'selectQuestion'], ['Grid Choice', 'gridChoice'], ['Free Text', 'freeText']]) %>
      </div>

      <div class="sm:col-span-2">
        <%= form.label :question %>
        <%= form.text_field :question %>
      </div>

      <div class="sm:col-span-2">
        <%= form.label :description %>
        <%= form.text_area :description, class: 'ckeditor' %>
      </div>
    <% end %>
  </div>

  <div class="admin-form-sticky-submit">
    <%= form.submit 'Save changes', data: { disable_with: 'Processing...' }, class: "admin-btn" %>
  </div>
<% end %>