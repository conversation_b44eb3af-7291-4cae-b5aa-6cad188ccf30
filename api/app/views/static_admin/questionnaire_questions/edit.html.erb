<%= render AdminPageBannerComponent.new(title: "Edit Question", back_to: { path: edit_admin_questionnaire_path(@questionnaire.id), text: "Back to Questionnaire" }) %>
<div>
  <% case @question.question_type %>
    <% when 'multiChoiceImage' %>
      <%= render "multi_choice_image_form" %>
    <% when 'eitherOr' %>
      <%= render "either_or_form" %>
    <% when 'selectQuestion' %>
      <%= render "select_question_form" %>
    <% when 'gridChoice' %>
      <%= render "grid_choice_form" %>
    <% when 'freeText' %>
      <%= render "free_text_form" %>
    <% else %>
      <%= render 'form' %>
  <% end %>
</div>
<hr class="mb-4"/>
<%= link_to 'Delete', admin_questionnaire_question_path(@questionnaire, @question), method: :delete, data: { confirm: 'Are you sure?' }, class: 'admin-btn admin-delete-btn' %>