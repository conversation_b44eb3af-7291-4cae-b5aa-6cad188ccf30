<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<%= render AdminPageBannerComponent.new(title: "Demographics", back_to: { path: statistics_admin_questionnaire_path(@questionnaire), text: "Back to Statistics" }) %>
<div class="flex-1 min-w-[300px]">
    <div class="mb-4">
      <%= form_with(url: statistics_admin_questionnaire_question_path(@questionnaire.id, @question.id), method: :get, local: true) do %>
        <div class="grid gap-x-4 w-full">
          <div class="admin-form-section bg-white rounded p-4 grid grid-cols-2 gap-4">
            <h3 class="mb-2 col-span-2">Filter the graphs by age, gender and ethnicity</h3>
            <div>
              <%= label_tag 'Date From' %>
              <%= date_field_tag :date_from, params[:date_from] %>
            </div>
            <div>
              <%= label_tag 'Date To' %>
              <%= date_field_tag :date_to, params[:date_to] %>
            </div>
            <div>
              <%= label_tag 'Lower age range' %>
              <%= number_field_tag :age_range_start, params[:age_range_start] %>
            </div>
            <div>
              <%= label_tag 'Upper age range' %>
              <%= number_field_tag :age_range_end, params[:age_range_end] %>
            </div>
            <div>
              <%= label_tag 'Gender' %>
              <%= select_tag :gender, options_for_select([['All', nil]] + @genders, selected: params[:gender]) %>
            </div>
            <div>
              <%= label_tag 'Ethnicity' %>
              <%= select_tag :ethnicity, options_for_select([['All', nil]] + @ethnicities, selected: params[:ethnicity]) %>
            </div>
            <div>
              <%= label_tag 'County' %>
              <%= select_tag :county, options_for_select([['All', nil]] + @counties, selected: params[:county]) %>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-x-4">
            <button class="mt-2 w-full admin-btn">Search</button>
            <%= link_to statistics_admin_questionnaire_question_path(@questionnaire.id, @question.id) do %>
                <button type="button" class="mt-2 w-full admin-btn">Reset Filters</button>
            <% end %>
        </div>
      <% end %>
    </div>

    <div class="p-8 bg-white rounded">
        <h2>Results by gender</h2>
        <canvas id="chart-gender" class="mb-4"></canvas>
        <h2>Results by ethnicity</h2>
        <canvas id="chart-ethnicity" class="mb-4"></canvas>
        <h2>Results by county</h2>
        <canvas id="chart-county" class="mb-4"></canvas>
    </div>
</div>

<script>
    const stats = <%= @data.to_json.html_safe %>;
    const questionType = <%= @question.question_type.to_json.html_safe %>
    const options = <%= @question.questionnaire_options.to_json.html_safe %>
    const charts = document.querySelectorAll('canvas');
    const labels = options.map(opt => questionType === 'selectQuestion' ? opt.option_data.label : opt.option_data.value);

    const bgColours = [
                        "#4E89CD",
                        "#7468B9",
                        "#54A763",
                        "#67D7D1",
                        "#261177",
                        "#DB8A90",
                        "#7A7E64",
                        "#4A8F21",
                        "#B42CB0",
                        "#E1D49E",
                        "#2234B3",
                        "#F3BA4A",
                        "#F9075A",
                        "#07DFB0",
                        "#8A9F8D",
                        "#661DE8",
                        "#096FD8",
                        "#C623AA",
                        "#3DBDA5",
                        "#C6F573",
                        "#1E2A48",
                    ];

    const parsedData = stats.map(item => {
        const answerObj = JSON.parse(item.answer);
        return {
            value: answerObj.answer_value,
            gender: item.gender,
            ethnicity: item.ethnicity,
            county: item.county
        };
    });

    const groupedData = parsedData.reduce((acc, curr) => {
        const { value, gender, ethnicity, county } = curr;

        if (!acc[value]) {
            acc[value] = {
                value: value,
                gender: {},
                ethnicity: {},
                county: {}
            };
        }

        acc[value].gender[gender] = (acc[value].gender[gender] || 0) + 1;
        acc[value].ethnicity[ethnicity] = (acc[value].ethnicity[ethnicity] || 0) + 1;
        acc[value].county[county] = (acc[value].county[county] || 0) + 1;

        return acc;
    }, {});

    const result = Object.values(groupedData);

    charts.forEach(chart => {
        const chartType = chart.id.split('-')[1];
        
        var keys = [...new Set(result.flatMap(item => Object.keys(item[chartType])))].filter(Boolean);

        var datasets = keys.map((key, i) => {
            return {
                label: key,
                data: result.map(item => item[chartType][key] || 0),
                backgroundColor: bgColours[i % bgColours.length]
            };
        });

        const ctx = document.getElementById(chart.id);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets
            },
            options: {
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    })
</script>