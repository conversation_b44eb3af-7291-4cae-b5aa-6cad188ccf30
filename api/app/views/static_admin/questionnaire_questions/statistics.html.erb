<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<% content_for :title, "Demographics | #{@question.question} | Question" %>
<%= render AdminPageBannerComponent.new(
  title: "Question Demographics",
  back_to: { path: statistics_admin_questionnaire_path(@questionnaire), text: "Back to Statistics" }
) %>

<div class="bg-gray-50 min-h-screen p-8">
  <!-- Header -->
  <div class="bg-gradient-to-br from-emerald-600 to-teal-700 text-white p-8 rounded-xl mb-8 shadow-lg">
    <h3 class="text-lg font-semibold mb-2">Question:</h3>
    <p class="text-white/90"><%= @question.question %></p>
  </div>

  <!-- Filters Section -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-8 overflow-hidden">
    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Demographic Filters</h3>
      <p class="text-sm text-gray-600 mt-1">Filter the demographic analysis by specific criteria to gain deeper insights into response patterns.</p>
    </div>
    <div class="p-6">
      <%= form_with(url: statistics_admin_questionnaire_question_path(@questionnaire.id, @question.id), method: :get, local: true, class: "space-y-6") do %>
        <!-- Date Range Filters -->
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Date Range</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= label_tag :date_from, 'Date From', class: "block text-sm font-medium text-gray-700" %>
              <%= date_field_tag :date_from, params[:date_from], class: "mt-1 focus:ring-emerald-500 focus:border-emerald-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
            </div>
            <div>
              <%= label_tag :date_to, 'Date To', class: "block text-sm font-medium text-gray-700" %>
              <%= date_field_tag :date_to, params[:date_to], class: "mt-1 focus:ring-emerald-500 focus:border-emerald-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
        </div>

        <!-- Age Range Filters -->
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Age Range</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= label_tag :age_range_start, 'Minimum Age', class: "block text-sm font-medium text-gray-700" %>
              <%= number_field_tag :age_range_start, params[:age_range_start], placeholder: "e.g. 16", class: "mt-1 focus:ring-emerald-500 focus:border-emerald-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
            </div>
            <div>
              <%= label_tag :age_range_end, 'Maximum Age', class: "block text-sm font-medium text-gray-700" %>
              <%= number_field_tag :age_range_end, params[:age_range_end], placeholder: "e.g. 25", class: "mt-1 focus:ring-emerald-500 focus:border-emerald-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
        </div>

        <!-- Demographic Filters -->
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Demographics</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <%= label_tag :gender, 'Gender', class: "block text-sm font-medium text-gray-700" %>
              <%= select_tag :gender, options_for_select([['All Genders', nil]] + @genders, selected: params[:gender]), class: "mt-1 block w-full focus:ring-emerald-500 focus:border-emerald-500 border-gray-300 rounded-md shadow-sm sm:text-sm" %>
            </div>
            <div>
              <%= label_tag :ethnicity, 'Ethnicity', class: "block text-sm font-medium text-gray-700" %>
              <%= select_tag :ethnicity, options_for_select([['All Ethnicities', nil]] + @ethnicities, selected: params[:ethnicity]), class: "mt-1 block w-full focus:ring-emerald-500 focus:border-emerald-500 border-gray-300 rounded-md shadow-sm sm:text-sm" %>
            </div>
            <div>
              <%= label_tag :county, 'County', class: "block text-sm font-medium text-gray-700" %>
              <%= select_tag :county, options_for_select([['All Counties', nil]] + @counties, selected: params[:county]), class: "mt-1 block w-full focus:ring-emerald-500 focus:border-emerald-500 border-gray-300 rounded-md shadow-sm sm:text-sm" %>
            </div>
          </div>
        </div>

        <div class="flex space-x-3">
          <%= submit_tag "Apply Filters", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500" %>
          <%= link_to statistics_admin_questionnaire_question_path(@questionnaire.id, @question.id), class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500" do %>
            Reset Filters
          <% end %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Demographics Charts -->
  <div class="space-y-8">
    <!-- Gender Demographics -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Results by Gender</h3>
        <p class="text-sm text-gray-600 mt-1">Response distribution across different gender identities</p>
      </div>
      <div class="p-6">
        <div class="h-80 relative">
          <canvas id="chart-gender"></canvas>
        </div>
      </div>
    </div>

    <!-- Ethnicity Demographics -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Results by Ethnicity</h3>
        <p class="text-sm text-gray-600 mt-1">Response patterns across ethnic backgrounds</p>
      </div>
      <div class="p-6">
        <div class="h-80 relative">
          <canvas id="chart-ethnicity"></canvas>
        </div>
      </div>
    </div>

    <!-- County Demographics -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Results by County</h3>
        <p class="text-sm text-gray-600 mt-1">Geographic distribution of responses across UK counties</p>
      </div>
      <div class="p-6">
        <div class="h-80 relative">
          <canvas id="chart-county"></canvas>
        </div>
      </div>
    </div>

    <% if @data.empty? %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
        <div class="text-gray-500">
          <i class="fa-solid fa-users text-4xl mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Demographic Data Available</h3>
          <p class="text-sm text-gray-500 mb-4">This question hasn't received any responses with demographic information yet, or your current filters exclude all responses.</p>
          <%= link_to statistics_admin_questionnaire_question_path(@questionnaire.id, @question.id), class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500" do %>
            Reset Filters
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const stats = <%= @data.to_json.html_safe %>;
    const questionType = <%= @question.question_type.to_json.html_safe %>;
    const options = <%= @question.questionnaire_options.to_json.html_safe %>;
    const labels = options.map(opt => questionType === 'selectQuestion' ? opt.option_data.label : opt.option_data.value);

    // Modern color palette matching campaigns impressions
    const colorPalette = [
        '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444',
        '#6366F1', '#EC4899', '#14B8A6', '#F97316', '#84CC16',
        '#6B7280', '#8B5A2B', '#7C3AED', '#059669', '#DC2626'
    ];

    const chartColors = {
        primary: '#10B981',
        secondary: '#14B8A6',
        accent: '#3B82F6'
    };

    const parsedData = stats.map(item => {
        const answerObj = JSON.parse(item.answer);
        return {
            value: answerObj.answer_value,
            gender: item.gender,
            ethnicity: item.ethnicity,
            county: item.county
        };
    });

    const groupedData = parsedData.reduce((acc, curr) => {
        const { value, gender, ethnicity, county } = curr;

        if (!acc[value]) {
            acc[value] = {
                value: value,
                gender: {},
                ethnicity: {},
                county: {}
            };
        }

        acc[value].gender[gender] = (acc[value].gender[gender] || 0) + 1;
        acc[value].ethnicity[ethnicity] = (acc[value].ethnicity[ethnicity] || 0) + 1;
        acc[value].county[county] = (acc[value].county[county] || 0) + 1;

        return acc;
    }, {});

    const result = Object.values(groupedData);

    const charts = document.querySelectorAll('canvas');
    charts.forEach(chart => {
        const chartType = chart.id.split('-')[1];

        var keys = [...new Set(result.flatMap(item => Object.keys(item[chartType])))].filter(Boolean);

        var datasets = keys.map((key, i) => {
            return {
                label: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                data: result.map(item => item[chartType][key] || 0),
                backgroundColor: colorPalette[i % colorPalette.length],
                borderWidth: 0,
                borderRadius: 6,
                borderSkipped: false
            };
        });

        const ctx = document.getElementById(chart.id);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: { size: 12 }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed.y / total) * 100).toFixed(1) : '0';
                                return `${context.dataset.label}: ${context.parsed.y} (${percentage}%)`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: '#f1f5f9' },
                        ticks: {
                            font: { size: 11 }
                        }
                    },
                    x: {
                        grid: { display: false },
                        ticks: {
                            font: { size: 11 },
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    });
});
</script>