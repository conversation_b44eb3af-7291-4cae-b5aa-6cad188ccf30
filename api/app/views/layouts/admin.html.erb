<!DOCTYPE html>
<html lang="<%= I18n.locale.presence || 'en' %>">
<head>
  <title><%= page_title("Admin") %></title>
  <script src="https://cdn.jsdelivr.net/npm/@cd2/fileboy-browser@0.12.1-alpha.7"></script>
  <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= javascript_importmap_tags %>
  <%= stylesheet_link_tag 'application' %>
  <%= javascript_include_tag 'application', type: "module" %>
  <%= javascript_include_tag "application", "data-turbo-track": "reload" %>
  <%= javascript_include_tag "nice-select", type: "module" %>
  <%= stylesheet_link_tag "tailwind", "inter-font" %>
  <script>
    window.mapboxToken = '<%= ENV['MAPBOX_API_KEY'] %>';
  </script>
  <meta name="viewport" content="width=device-width">
  <link
    rel="stylesheet"
    href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css"
    />
  <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
  <link href="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.css" rel="stylesheet">
  <script src="https://api.mapbox.com/mapbox-gl-js/v3.12.0/mapbox-gl.js"></script>
  <script defer src="https://kit.fontawesome.com/7fae4ae66d.js" crossorigin="anonymous"></script>
  <%= render './layouts/common_header' %>
  <script>
    function withCharts(cb) {
      const safeCb = () => {
        try {
          cb();
        } catch (e) {
          console.error("Failed to render charts cb")
          console.error(e);
        }
      }
      if (window.google && window.google.charts && window.google.visualization && window.google.visualization.DataTable && window.google.visualization.PieChart) {
        safeCb();
        return;
      }
      if (window._chartsLoading) {
        window._chartsLoading.push(safeCb);
        return;
      }
      window._chartsLoading = [safeCb];
      const src = "https://www.gstatic.com/charts/loader.js"
      const script = document.createElement("script")
      script.src = src
      script.async = true
      script.onload = () => {
        google.charts.load('current', {'packages':['corechart', 'bar']});
        google.charts.setOnLoadCallback(() => {
          setTimeout(()=> {
            window._chartsLoading.forEach(cb => cb());
            delete window._chartsLoading;
          }, 1000)
        });
      }
      document.head.appendChild(script)
    }
  </script>
  <script>
    function withMaps(cb) {
      const url = "https://maps.googleapis.com/maps/api/js?key=<%= ENV["GOOGLE_MAPS_API_KEY"] %>&callback=__mapsLoaded"
      if (window.google && window.google.maps) {
        cb();
        return;
      }
      if (window._mapsLoading) {
        window._mapsLoading.push(cb);
        return;
      }
      window._mapsLoading = [cb];
      const script = document.createElement("script")
      script.src = url
      script.async = true
      window.__mapsLoaded = () => {
        window._mapsLoading.forEach(cb => cb());
        delete window._mapsLoading;
      }
      document.head.appendChild(script)
    }
  </script>
  <script>
    // Just some utils
    function titleize(str) {
      return str.replace(/_/g, " ").replace(/\b\w/g, c => c.toUpperCase());
    }

    // args: [arr: number[], num: number]
    // rets: number[]
    function projectData(arr, num) {
      if (arr.length === 0) return []

      // compute average growth rate
      const deltas = []
      for (let i = 1; i < arr.length; i++) {
        deltas.push(arr[i] - arr[i - 1])
      }
      const growthRate = deltas.reduce((acc, delta) => acc + delta, 0) / deltas.length

      // project data
      const projectedData = []
      let lastValue = arr[arr.length - 1]
      for (let i = 0; i < num; i++) {
        lastValue = Math.max(lastValue + growthRate, 0)
        projectedData.push(lastValue)
      }

      return projectedData
    }

    function smoothArray(arr) {
      if (arr.length < 3) {
        return arr; // Cant smooth if there are less than 3 elements
      }

      const smoothedArr = [];
      smoothedArr.push(arr[0]); // First element remains unchanged

      for (let i = 1; i < arr.length - 1; i++) {
        const smoothedValue = (arr[i - 1] + arr[i] + arr[i + 1]) / 3;
        smoothedArr.push(smoothedValue);
      }

      smoothedArr.push(arr[arr.length - 1]); // Last element remains unchanged

      return smoothedArr;
    }

    // month: string, returns string[]
    // example input: "2021-01", output: ["2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07"]
    function computeNextMonths(month, numMonths = 6) {
      const [year, monthStr] = month.split('-').map(Number);
      const months = [];
      const startMonthTotal = year * 12 + (monthStr - 1); // Total months since year 0
      for (let i = 1; i <= numMonths; i++) {
        const nextMonthTotal = startMonthTotal + i;
        const newYear = Math.floor(nextMonthTotal / 12);
        const newMonth = (nextMonthTotal % 12) + 1;
        const formattedMonth = String(newMonth).padStart(2, '0');
        months.push(`${newYear}-${formattedMonth}`);
      }
      return months;
    }

    // date = 2020-03
    function nextYearMonthString(date) {
      const [year, month] = date.split('-').map(Number)
      if (month === 12) {
        return `${year + 1}-01`
      } else {
        return `${year}-${String(month + 1).padStart(2, '0')}`
      }
    }
  </script>
  <script defer src="https://cdn.jsdelivr.net/npm/htmx.org@1.9.6"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
</head>
<body>
<div class="flex bg-gradient-to-b from-[#101827] to-dark-blue min-h-[100dvh] nova-headings">
  <!-- Sidebar -->
  <div class="sidebar-accordion">
    <aside class="w-full bg-white px-4 py-8 text-black text-sm flex flex-col top-0 h-full space-y-6 rounded-tr-3xl font-medium" aria-label="Sidebar" data-controller="toggle">
      <%= link_to root_path do %>
        <img src="https://www.developingexperts.com/file-cdn/images/get/92ff4093-f09c-4ab0-9851-cf76204f019c+logo" style="width: 140px"/>
      <% end %>

      <div class="sidebar-scroll-section overflow-y-auto flex-1">
      <div class="space-y-6">
        <ul class="-mt-8 -mb-2">
          <li><%= link_to 'Dashboard', admin_root_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'dashboard') %></li>
        </ul>
        <% if @current_user&.allows_admin_section?(:curriculum) %>
          <div>
            <div>
              <p class="text-de-brand mb-2 font-semibold text-lg">
                Curriculum
              </p>
              <ul class="space-y-2">
                <li><%= link_to 'Curricula', admin_curricula_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'curricula') %></li>
                <li><%= link_to 'Subjects', admin_subjects_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'subjects') %></li>
                <li><%= link_to 'Years', admin_years_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'years') %></li>
                <li><%= link_to 'Units', admin_units_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'units') %></li>
                <li><%= link_to 'Enquiry Skills and Approaches', admin_scientific_enquiry_types_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'enquiry_skills') %></li>
                <li><%= link_to 'Curriculum Definitions', admin_curriculum_definitions_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'curriculum_definitions') %></li>
                <li><%= link_to 'Curriculum Documents', admin_curriculum_documents_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'curriculum_documents') %></li>
                <li><%= link_to 'Lesson Templates', admin_lesson_templates_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'lesson_templates') %></li>
                <li><%= link_to 'Lesson Feedback', admin_lesson_feedbacks_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'lesson_feedbacks') %></li>
                <li><%= link_to 'Questionnaires', admin_questionnaires_path, class: ("text-de-brand" if controller_name == 'questionnaires' || controller_name == 'questionnaire_questions') %></li>
              </ul>
            </div>
          </div>
        <% end %>
        <% if @current_user&.allows_admin_section?(:content) %>
          <div>
            <div>
              <p class="text-de-brand mb-2 font-semibold text-lg">
                Content
              </p>
              <ul class="space-y-2">
                <li><%= link_to 'Videos', admin_videos_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'videos') %></li>
                <li><%= link_to 'Images', admin_images_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'images') %></li>
                <% if @current_user&.beta_feature_enabled?(:aug_4) %>
                  <li><%= link_to 'Documents', admin_documents_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'documents') %></li>
                <% end %>
                <li><%= link_to 'Glossaries', admin_glossaries_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'glossaries') %></li>
                <li><%= link_to '360 Tours', admin_tours_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'tours') %></li>
                <li><%= link_to 'Articles', admin_articles_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'articles') %></li>
                <li><%= link_to 'Careers', admin_careers_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'careers') %></li>
                <li><%= link_to 'AI Careers', admin_career_builder_index_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'career_builder') %></li>
                <li><%= link_to 'Job Families', admin_job_families_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'job_families') %></li>
                <li><%= link_to 'Exemplar Works', admin_exemplar_works_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'exemplar_works') %></li>
                <li><%= link_to 'Quizzes', admin_quip_quizzes_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'quip_quizzes') %></li>
                <li><%= link_to 'Quiz Results', admin_quip_quiz_results_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'quip_quiz_results') %></li>
                <li><%= link_to 'Live Streams', admin_live_streams_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'live_streams') %></li>
              </ul>
            </div>
          </div>
        <% end %>
        <% if @current_user&.allows_admin_section?(:website) %>
          <div>
            <p class="text-de-brand mb-2 font-semibold text-lg">
              Website
            </p>
            <ul class="space-y-2">
              <li><%= link_to 'Accreditations', admin_accreditations_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'accreditations') %></li>
              <li><%= link_to 'Team', admin_team_members_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'team_members') %></li>
              <li><%= link_to 'Jobs', admin_job_listings_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'job_listings') %></li>
              <li><%= link_to 'Events', admin_events_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'events') %></li>
              <li><%= link_to 'Campaigns', admin_campaigns_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'campaigns') %></li>
            </ul>
          </div>
        <% end %>
        <% if @current_user&.allows_admin_section?(:schools) %>
          <div>
            <div>
              <p class="text-de-brand mb-2 font-semibold text-lg">
                Schools
              </p>
              <ul class="space-y-2">
                <li><%= link_to 'Schools', admin_static_schools_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'schools') %></li>
                <li><%= link_to 'Dashboard Messages', admin_motds_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'motds') %></li>
                <li><%= link_to 'Presentation Feedback', admin_presentation_feedbacks_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'presentation_feedbacks') %></li>
                <li><%= link_to 'Subscription Management', admin_subscription_dashboard_index_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'subscription_dashboard') %></li>
                <% if @current_user&.beta_feature_enabled?(:aug_18) %>
                  <li><%= link_to 'Dashboard Notifications', admin_dashboard_notifications_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'dashboard_notifications') %></li>
                <% end %>
              </ul>
            </div>
          </div>
        <% end %>
        <% if @current_user&.allows_admin_section?(:marketing) %>
          <div>
            <div>
              <p class="text-de-brand mb-2 font-semibold text-lg">
                Marketing
              </p>
              <ul class="space-y-2">
                <li><%= link_to 'Affiliates', admin_affiliates_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'affiliates') %></li>
                <li><%= link_to 'Custom Sign Up URLs', admin_custom_sign_up_urls_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'custom_sign_up_urls') %></li>
                <li><%= link_to 'Sign Up Events', admin_sign_up_events_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'sign_up_events') %></li>
                <li><%= link_to 'Organisations', admin_organisations_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'organisations') %></li>
                <li><%= link_to 'Social Messages', admin_de_socials_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'de_socials') %></li>
                <li><%= link_to 'Generate Tracking Link', new_admin_marketing_link_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'marketing_links') %></li>
                <li><%= link_to 'Mailchimp', admin_mailchimp_index_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'mailchimp') %></li>
                <% if @current_user&.beta_feature_enabled?(:aug_18) %>
                  <li><%= link_to 'Event Registrations', admin_event_registrations_path, class: ("text-de-brand underline decoration-1 underline-offset-2" if controller_name == 'event_registrations') %></li>
                <% end %>
              </ul>
            </div>
          </div>
        <% end %>
        <% if @current_user&.allows_admin_section?(:misc) %>
          <div>
            <div>
              <p class="text-de-brand mb-2 font-semibold text-lg">
                Misc
              </p>
              <ul class="space-y-2">
                <li><%= link_to 'Error Logs', admin_error_logs_path, class: ("text-de-brand" if controller_name == 'error_logs') %></li>
              <li><%= link_to 'Legacy Stripe Subscriptions', admin_stripe_subscriptions_path, class: ("text-de-brand" if controller_name == 'stripe_subscriptions') %></li>
              <li><%= link_to 'Users', admin_users_path, class: ("text-de-brand" if controller_name == 'users') %></li>
              <li><%= link_to 'Flagged Images', admin_flagged_images_path, class: ("text-de-brand" if controller_name == 'flagged_images') %></li>
              <li><%= link_to 'Referrals', admin_referrals_path, class: ("text-de-brand" if controller_name == 'referrals') %></li>
              <% if @current_user&.beta_feature_enabled?(:aug_18) %>
                <li><%= link_to 'New Referrals', admin_new_referrals_path, class: ("text-de-brand" if controller_name == 'new_referrals') %></li>
              <% end %>
              <li><%= link_to 'Upload UK Schools', admin_uk_schools_uploader_index_path, class: ("text-de-brand" if controller_name == 'uk_schools_uploader') %></li>
              </ul>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </aside>
  </div>


  <!-- Content area -->
  <div class="flex-1 py-8 px-4 sm:px-12 content-container w-full">
    <%= render '/htmx_components/static_session_header', user: @current_user %>
    <%= render 'shared/flash_banner' %>
    <div id="preview-video-loader" style="display: none;"></div>
    <div class="py-8">
      <%= yield %>
    </div>
  </div>
  <div id="toast-container"></div>
  <div hx-trigger="load" hx-get="/dev_toolbar" hx-swap="outerHtml" hx-target="this"></div>
</div>
<% if @current_user&.beta_feature_enabled?(:september_1) %>
  <script src="https://cdn.jsdelivr.net/gh/isaul32/ckeditor5@c3463fe834049bf5d805d1d22402108a9c0576bd/packages/ckeditor5-build-classic/build/ckeditor.js"></script>
<% else %>
  <script src="https://cdn.ckeditor.com/ckeditor5/40.2.0/classic/ckeditor.js"></script>
<% end %>
<script>
    class FileboyUploadAdapter {
        constructor(loader) {
            this.loader = loader;
        }

        upload() {
            return this.loader.file.then(file => {
                return new Promise((resolve, reject) => {
                    const data = new FormData();
                    data.append('upload', file);

                    fetch('/images', {
                        method: 'POST',
                        body: data
                    })
                        .then(response => response.json())
                        .then(result => {
                            if (result.uploaded) {
                                resolve({default: result.url});
                            } else {
                                reject(result.error.message);
                            }
                        })
                        .catch(error => {
                            reject('File upload failed');
                        });
                });
            });
        }
    }

    function FileboyUploadAdapterPlugin(editor) {
        editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
            return new FileboyUploadAdapter(loader);
        };
    }

    document.querySelectorAll('.ckeditor').forEach(function (e) {
        ClassicEditor
            .create(e, {
                math: {
                  engine: 'mathjax',
                  outputType: 'script',
                  forceOutputType: false,
                  enablePreview: true
                },
                extraPlugins: [FileboyUploadAdapterPlugin],
                // ... other CKEditor configurations ...
            })
            .catch(error => {
                console.error(error);
            });
    });

</script>
</body>
