<div class="text-white">
  <%= render AdminPageBannerComponent.new(title: @question[:question]) %>
  <%== @question[:description] %>
  <form>
    <div class="mx-auto grid grid-cols-[repeat(auto-fit,_220px)] gap-4 my-4">
      <% @question.questionnaire_options.each do |option| %>
        <% opt_id = option[:id] %>
        <label class="grid gap-y-2">
          <div class="flex h-56 w-56">
            <input id="check-<%= opt_id %>" type="checkbox" class="hidden peer" value="<%= option[:option_data]["value"] %>"/>
            <div class="h-52 w-52 peer-checked:h-56 peer-checked:w-56 m-auto rounded-full bg-cover border-[6px] border-[#3a5cc8] hover:border-[#39b5b1] peer-checked:border-[#ffcd00] transition-all overflow-hidden" style="background-image: url('https://www.developingexperts.com/file-cdn/images/get/<%= option[:option_data]["fileboyId"] %>?transform=resize:200x200')">
              <div class="flex h-full w-full bg-black/50">
                <p class="text-xl text-center p-3 m-auto"><%= option[:option_data]["value"] %></p>
              </div>
            </div>
          </div>
          <div class="grid grid-cols-[max-content_80px_max-content] text-black gap-x-2 justify-center">
            <button id="dec-<%= opt_id %>" type="button" class="bg-white rounded-lg text-lg font-bold h-9 w-9 m-auto"><i class="fa-solid fa-minus"></i></button>
            <input id="num-<%= opt_id %>" type="number" class="rounded-lg" min="1"/>
            <button id="inc-<%= opt_id %>" type="button" class="bg-white rounded-lg text-lg font-bold h-9 w-9 m-auto"><i class="fa-solid fa-plus"></i></button>
          </div>
        </label>
      <% end %>
    </div>
    <div class="flex justify-between">
      <button type="button" class="btn-primary flex gap-x-8" id="backBtn" onclick="prevQuestionnaireStep()">
        <i class="fa-solid fa-arrow-left my-auto"></i>
        <span>Back</span>
      </button>
      <button type="submit" class="btn-primary flex gap-x-8" id="nextBtn" disabled>
        <span>Next</span>
        <i class="fa-solid fa-arrow-right my-auto"></i>
      </button>
    </div>
  </form>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    var checkboxes = document.querySelectorAll("[id^='check-']");
    var numFields = document.querySelectorAll("[id^='num-']");
    var decrementBtns = document.querySelectorAll("[id^='dec-']");
    var incrementBtns = document.querySelectorAll("[id^='inc-']");
    var nextBtn = document.getElementById("nextBtn");

    const answers = <%= @answers.to_json.html_safe %>;

    if (answers?.length) {
      nextBtn.disabled = false;
      const optionCount = {};
      answers.forEach(answer => {
        const optionId = answer.questionnaire_option_id;
        optionCount[optionId] = (optionCount[optionId] || 0) + 1;
      });

      Object.keys(optionCount).forEach(optionId => {
        const checkbox = document.getElementById(`check-${optionId}`);
        if (checkbox) {
            checkbox.checked = true;
        }
        
        const numberField = document.getElementById(`num-${optionId}`);
        if (numberField) {
            numberField.value = optionCount[optionId];
        }
      });
    }

    checkboxes.forEach(checkbox => {
      checkbox.addEventListener("change", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        numField.value = this.checked ? 1 : null;

        const isChecked = Array.from(checkboxes).some(checkbox => checkbox.checked);
        nextBtn.disabled = !isChecked;
      });
    });

    numFields.forEach(field => {
      field.addEventListener("change", function() {
        const optId = this.id.split('-')[1];
        document.getElementById(`check-${optId}`).checked = true;

        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });

    decrementBtns.forEach(btn => {
      btn.addEventListener("click", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        let currentValue = parseInt(numField.value, 10);
        if (currentValue > 1) {
          numField.value = currentValue - 1;
        } else {
          numField.value = null;
          document.getElementById(`check-${optId}`).checked = false;
        }

        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });

    incrementBtns.forEach(btn => {
      btn.addEventListener("click", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        let currentValue = parseInt(numField.value, 10);
        numField.value = currentValue + 1 || 1;
        document.getElementById(`check-${optId}`).checked = true;

        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });

    document.querySelector('form').addEventListener('submit', function(event) {
      event.preventDefault();
      
      const numFields = document.querySelectorAll('input[id^="num-"]');
      
      let formData = {};

      numFields.forEach(function(input) {
        let value = parseInt(input.value, 10);
        
        if (value > 0) {
          formData[input.id.split('-')[1]] = value;
        }
      });

      fetch('/school/questionnaires/<%= @question[:id] %>/submit', {
        method: 'POST',
        headers: {'Content-Type': 'application/json',},
        body: JSON.stringify({formData})
      }).then(response => {
          if (response.redirected) {
              window.location.href = response.url;
          }
      }).catch(e => {
          console.error('An error occurred', e);
      });
    });
  });

  function prevQuestionnaireStep() {
    fetch('/school/questionnaires/<%= @question[:id] %>/back', {
      method: 'GET',
    }).then(response => {
        if (response.redirected) {
            window.location.href = response.url;
        }
    }).catch(e => {
        console.error('An error occurred', e);
    });
  }
</script>