<div class="text-white">
  <% groups = @question.questionnaire_options.group_by { |opt| opt[:option_data]["group"] } %>
  <div class="flex justify-between">
    <%= render AdminPageBannerComponent.new(title: @question[:question]) %>
    <p class="font-semibold text-3xl">(<span id="question_no">1</span>/<%= groups.count %>)</p>
  </div>
  <%== @question[:description] %>
  <form>
    <% groups.each_with_index do |(group, options), i| %>
      <div id="eitherStep-<%= i %>" class="grid grid-cols-[repeat(auto-fit,_minmax(300px,_1fr))] gap-4 my-4 <%= i == 0 ? '' : 'hidden' %>">
        <% options.each do |option| %>
          <% value = option[:option_data]["value"] %>
          <% opt_id = option[:id] %>
          <label class="grid gap-y-2">
            <input id="check-<%= opt_id %>" type="checkbox" name="<%= group %>" class="hidden peer" value="<%= opt_id %>"/>
            <div class="bg-white border-[6px] border-[#3a5cc8] hover:border-[#39b5b1] peer-checked:border-[#ffcd00] transition-all rounded">
              <div class="flex">
                <p class="text-2xl text-black text-center p-4 m-auto"><%= value %></p>
              </div>
            </div>
            <div class="grid grid-cols-[max-content_80px_max-content] text-black gap-x-2 justify-center">
              <button id="dec-<%= opt_id %>" type="button" class="bg-white rounded-lg text-lg font-bold h-9 w-9 m-auto"><i class="fa-solid fa-minus"></i></button>
              <input id="num-<%= opt_id %>" type="number" class="rounded-lg" min="1"/>
              <button id="inc-<%= opt_id %>" type="button" class="bg-white rounded-lg text-lg font-bold h-9 w-9 m-auto"><i class="fa-solid fa-plus"></i></button>
            </div>
          </label>
        <% end %>
      </div>
    <% end %>
  </form>
</div>

<div class="flex justify-between">
  <button type="button" class="btn-primary flex gap-x-8" id="backBtn" onclick="prevStep()">
    <i class="fa-solid fa-arrow-left my-auto"></i>
    <span>Back</span>
  </button>
  <div class="flex gap-x-2">
    <button type="submit" class="btn-primary flex gap-x-8" id="nextBtn" onclick="nextStep()" disabled>
      <span>Next</span>
      <i class="fa-solid fa-arrow-right my-auto"></i>
    </button>
    <button type="button" class="btn-secondary" id="skipBtn" onclick="skipStep()">Skip</button>
  </div>
</div>

<script>
  var currentStep = 0;
  
  const options = <%= @question.questionnaire_options.to_json.html_safe %>;
  const stepsLength = new Set(options.map(opt => opt.option_data.group)).size;
  const questionNoSpan = document.getElementById("question_no");

  const answers = <%= @answers.to_json.html_safe %>;

  if (answers?.length) {
    const optionCount = {};
    answers.forEach(answer => {
      const optionId = answer.questionnaire_option_id;
      optionCount[optionId] = (optionCount[optionId] || 0) + 1;
    });

    Object.keys(optionCount).forEach(optionId => {
      const checkbox = document.getElementById(`check-${optionId}`);
      if (checkbox) {
          checkbox.checked = true;
      }
      
      const numberField = document.getElementById(`num-${optionId}`);
      if (numberField) {
          numberField.value = optionCount[optionId];
      }
    });
  }

  function prevStep() {
    if (currentStep > 0) {
      currentStep--;
      showStep(currentStep);
      questionNoSpan.innerHTML = currentStep + 1;
      setFields();
    } else {
      fetch('/school/questionnaires/<%= @question[:id] %>/back', {
        method: 'GET',
      }).then(response => {
          if (response.redirected) {
              window.location.href = response.url;
          }
      }).catch(e => {
          console.error('An error occurred', e);
      });
    }
  }

  function nextStep() {
    if (currentStep + 1 < stepsLength) {
      currentStep++;
      showStep(currentStep);
      questionNoSpan.innerHTML = currentStep + 1;
      setFields();
    } else {
      const numFields = document.querySelectorAll('input[id^="num-"]');
      
      let formData = {};

      numFields.forEach(function(input) {
        let value = parseInt(input.value, 10);
        
        if (value > 0) {
          formData[input.id.split('-')[1]] = value;
        }
      });

      fetch('/school/questionnaires/<%= @question[:id] %>/submit', {
        method: 'POST',
        headers: {'Content-Type': 'application/json',},
        body: JSON.stringify({formData})
      }).then(response => {
          if (response.redirected) {
              window.location.href = response.url;
          }
      }).catch(e => {
          console.error('An error occurred', e);
      });
    }
  }

  function skipStep() {
    var step = document.getElementById(`eitherStep-${currentStep}`);
    var checkboxes = step.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(function(checkbox) {
      checkbox.checked = false;
    });
    nextStep();
  }

  function showStep(n) {
    const steps = document.querySelectorAll("[id^='eitherStep-']");

    steps.forEach(step => {
      if (step.id === `eitherStep-${n}`) {
        step.style.display = 'grid';
      } else {
        step.style.display = 'none';
      }
    });
  }

  function setFields() {
     var currentStepDiv = document.getElementById(`eitherStep-${currentStep}`);
  
    var checkboxes = currentStepDiv.querySelectorAll("[id^='check-']");
    var numFields = currentStepDiv.querySelectorAll("[id^='num-']");
    var decrementBtns = currentStepDiv.querySelectorAll("[id^='dec-']");
    var incrementBtns = currentStepDiv.querySelectorAll("[id^='inc-']");
    var nextBtn = document.getElementById("nextBtn");

    const isChecked = Array.from(checkboxes).some(checkbox => checkbox.checked);
    nextBtn.disabled = !isChecked;

    checkboxes.forEach(checkbox => {
      checkbox.addEventListener("change", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        numField.value = this.checked ? 1 : null;

        const isChecked = Array.from(checkboxes).some(checkbox => checkbox.checked);
        nextBtn.disabled = !isChecked;
      });
    });

    numFields.forEach(field => {
      field.addEventListener("change", function() {
        const optId = this.id.split('-')[1];
        document.getElementById(`check-${optId}`).checked = true;
        
        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });

    decrementBtns.forEach(btn => {
      btn.addEventListener("click", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        let currentValue = parseInt(numField.value, 10);
        if (currentValue > 1) {
          numField.value = currentValue - 1;
        } else {
          numField.value = null;
          document.getElementById(`check-${optId}`).checked = false;
        }

        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });

    incrementBtns.forEach(btn => {
      btn.addEventListener("click", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        let currentValue = parseInt(numField.value, 10);
        numField.value = currentValue + 1 || 1;
        document.getElementById(`check-${optId}`).checked = true;

        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });
  };

  document.addEventListener("DOMContentLoaded", function() {
    setFields();
  });
</script>