<div class="text-white">
  <%= render AdminPageBannerComponent.new(title: @question[:question]) %>
  <%== @question[:description] %>
  <form>
    <div class="grid gap-4 my-4">
      <% @question.questionnaire_options.order("weight asc").each do |option| %>
        <% opt_id = option[:id] %>
        <div class="flex justify-between bg-white text-black rounded p-4">
          <h4 class="my-auto text-xl"><%= option[:option_data]["label"] %></h4>
          <div class="grid grid-cols-[max-content_80px_max-content] text-black gap-x-2 justify-center">
            <button id="dec-<%= opt_id %>" type="button" class="bg-white rounded-lg text-lg font-bold h-9 w-9 m-auto" style="box-shadow:rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;"><i class="fa-solid fa-minus"></i></button>
            <input id="num-<%= opt_id %>" type="number" class="rounded-lg" value="0" min="0"/>
            <button id="inc-<%= opt_id %>" type="button" class="bg-white rounded-lg text-lg font-bold h-9 w-9 m-auto" style="box-shadow:rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;"><i class="fa-solid fa-plus"></i></button>
          </div>
        </div>
      <% end %>
    </div>
    <div class="flex justify-between">
      <button type="button" class="btn-primary flex gap-x-8" id="backBtn" onclick="prevQuestionnaireStep()">
        <i class="fa-solid fa-arrow-left my-auto"></i>
        <span>Back</span>
      </button>
      <button type="submit" class="btn-primary flex gap-x-8" id="nextBtn" disabled>
        <span>Next</span>
        <i class="fa-solid fa-arrow-right my-auto"></i>
      </button>
    </div>
  </form>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    var numFields = document.querySelectorAll("[id^='num-']");
    var decrementBtns = document.querySelectorAll("[id^='dec-']");
    var incrementBtns = document.querySelectorAll("[id^='inc-']");
    var nextBtn = document.getElementById("nextBtn");

    const answers = <%= @answers.to_json.html_safe %>;

    if (answers?.length) {
      nextBtn.disabled = false;
      const optionCount = {};
      answers.forEach(answer => {
        const optionId = answer.questionnaire_option_id;
        optionCount[optionId] = (optionCount[optionId] || 0) + 1;
      });

      Object.keys(optionCount).forEach(optionId => {        
        const numberField = document.getElementById(`num-${optionId}`);
        if (numberField) {
            numberField.value = optionCount[optionId];
        }
      });
    }

    decrementBtns.forEach(btn => {
      btn.addEventListener("click", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        let currentValue = parseInt(numField.value, 10);
        if (currentValue > 0) {
          numField.value = currentValue - 1;
        }

        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });

    incrementBtns.forEach(btn => {
      btn.addEventListener("click", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        let currentValue = parseInt(numField.value, 10);
        numField.value = currentValue + 1;

        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });

    numFields.forEach(function (numField) {
      numField.addEventListener('input', function () {
        const hasNonZeroValue = Array.from(numFields).some(field => parseInt(field.value) > 0);
        nextBtn.disabled = !hasNonZeroValue;
      });
    });

    document.querySelector('form').addEventListener('submit', function(event) {
      event.preventDefault();
      
      const numFields = document.querySelectorAll('input[id^="num-"]');
      
      let formData = {};

      numFields.forEach(function(input) {
        let value = parseInt(input.value, 10);
        
        if (value > 0) {
          formData[input.id.split('-')[1]] = value;
        }
      });

      fetch('/school/questionnaires/<%= @question[:id] %>/submit', {
        method: 'POST',
        headers: {'Content-Type': 'application/json',},
        body: JSON.stringify({formData})
      }).then(response => {
          if (response.redirected) {
              window.location.href = response.url;
          }
      }).catch(e => {
          console.error('An error occurred', e);
      });
    });
  });

  function prevQuestionnaireStep() {
    fetch('/school/questionnaires/<%= @question[:id] %>/back', {
      method: 'GET',
    }).then(response => {
        if (response.redirected) {
            window.location.href = response.url;
        }
    }).catch(e => {
        console.error('An error occurred', e);
    });
  }
</script>