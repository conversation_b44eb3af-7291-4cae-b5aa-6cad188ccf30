<div class="text-white">
  <% groups = @question.questionnaire_options.each_slice(12) %>
  <div class="flex justify-between">
    <%= render AdminPageBannerComponent.new(title: @question[:question]) %>
    <p class="font-semibold text-3xl">(<span id="pageNo">1</span>/<%= groups.count %>)</p>
  </div>
  <%== @question[:description] %>
  <form>
    <% groups.with_index do |chunk, i| %>
      <div id="page-<%= i %>" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 my-4 <%= i == 0 ? '' : 'hidden' %>">
        <% chunk.each do |option| %>
          <% opt_id = option[:id] %>
          <label class="h-full">
            <input id="check-<%= opt_id %>" type="checkbox" class="hidden peer" value="<%= option[:option_data]["value"] %>"/>
            <div class="bg-white border-[6px] border-[#3a5cc8] hover:border-[#39b5b1] peer-checked:border-[#ffcd00] transition-all rounded h-full">
              <div class="w-full aspect-[2/1] bg-cover bg-center" style="background-image: url('https://www.developingexperts.com/file-cdn/images/get/<%= option[:option_data]["fileboyId"] %>?transform=resize:200x200')"></div>
              <div class="flex">
                <h4 class="text-xl text-black text-center p-4 m-auto"><%= option[:option_data]["value"] %></h4>
              </div>
              <div class="grid grid-cols-[max-content_80px_max-content] text-black gap-x-2 justify-center mb-4">
                <button id="dec-<%= opt_id %>" type="button" class="bg-white rounded-lg text-lg font-bold h-9 w-9 m-auto" style="box-shadow:rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;"><i class="fa-solid fa-minus"></i></button>
                <input id="num-<%= opt_id %>" type="number" class="rounded-lg" min="1"/>
                <button id="inc-<%= opt_id %>" type="button" class="bg-white rounded-lg text-lg font-bold h-9 w-9 m-auto" style="box-shadow:rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;"><i class="fa-solid fa-plus"></i></button>
              </div>
            </div>
          </label>
        <% end %>
      </div>
    <% end %>
    <div class="flex justify-between">
      <button type="button" class="btn-primary flex gap-x-8" id="backBtn" onclick="prevPage()">
        <i class="fa-solid fa-arrow-left my-auto"></i>
        <span>Back</span>
      </button>
      <button type="button" class="btn-primary flex gap-x-8" id="nextBtn" onclick="nextPage()">
        <span>Next</span>
        <i class="fa-solid fa-arrow-right my-auto"></i>
      </button>
    </div>
  </form>
</div>

<script>
  var currentPage = 0;
  const options = <%= @question.questionnaire_options.to_json.html_safe %>;
  const pagesLength = options.length / 12;
  const pageNoSpan = document.getElementById("pageNo");

  function prevPage() {
    if (currentPage > 0) {
      currentPage--;
      showPage(currentPage);
      pageNoSpan.innerHTML = currentPage + 1;
    } else {
      fetch('/school/questionnaires/<%= @question[:id] %>/back', {
        method: 'GET',
      }).then(response => {
          if (response.redirected) {
              window.location.href = response.url;
          }
      }).catch(e => {
          console.error('An error occurred', e);
      });
    }
  }

  function nextPage() {
    if (currentPage + 1 < pagesLength) {
      currentPage++;
      showPage(currentPage);
      pageNoSpan.innerHTML = currentPage + 1;
    } else {
      const numFields = document.querySelectorAll('input[id^="num-"]');
      
      let formData = {};

      numFields.forEach(function(input) {
        let value = parseInt(input.value, 10);
        
        if (value > 0) {
          formData[input.id.split('-')[1]] = value;
        }
      });

      fetch('/school/questionnaires/<%= @question[:id] %>/submit', {
        method: 'POST',
        headers: {'Content-Type': 'application/json',},
        body: JSON.stringify({formData})
      }).then(response => {
          if (response.redirected) {
              window.location.href = response.url;
          }
      }).catch(e => {
          console.error('An error occurred', e);
      });
    }
  }

  function showPage(n) {
    const pages = document.querySelectorAll("[id^='page-']");

    pages.forEach(page => {
      if (page.id === `page-${n}`) {
        page.style.display = 'grid';
      } else {
        page.style.display = 'none';
      }
    });
  }

  document.addEventListener("DOMContentLoaded", function() {
    var checkboxes = document.querySelectorAll("[id^='check-']");
    var numFields = document.querySelectorAll("[id^='num-']");
    var decrementBtns = document.querySelectorAll("[id^='dec-']");
    var incrementBtns = document.querySelectorAll("[id^='inc-']");

    const answers = <%= @answers.to_json.html_safe %>;

    if (answers?.length) {
      const optionCount = {};
      answers.forEach(answer => {
        const optionId = answer.questionnaire_option_id;
        optionCount[optionId] = (optionCount[optionId] || 0) + 1;
      });

      Object.keys(optionCount).forEach(optionId => {
        const checkbox = document.getElementById(`check-${optionId}`);
        if (checkbox) {
            checkbox.checked = true;
        }
        
        const numberField = document.getElementById(`num-${optionId}`);
        if (numberField) {
            numberField.value = optionCount[optionId];
        }
      });
    }

    checkboxes.forEach(checkbox => {
      checkbox.addEventListener("change", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        numField.value = this.checked ? 1 : null;
      });
    });

    numFields.forEach(field => {
      field.addEventListener("change", function() {
        const optId = this.id.split('-')[1];
        document.getElementById(`check-${optId}`).checked = true;
      });
    });

    decrementBtns.forEach(btn => {
      btn.addEventListener("click", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        let currentValue = parseInt(numField.value, 10);
        if (currentValue > 1) {
          numField.value = currentValue - 1;
        } else {
          numField.value = null;
          document.getElementById(`check-${optId}`).checked = false;
        }
      });
    });

    incrementBtns.forEach(btn => {
      btn.addEventListener("click", function() {
        const optId = this.id.split('-')[1];
        const numField = document.getElementById(`num-${optId}`);
        let currentValue = parseInt(numField.value, 10);
        numField.value = currentValue + 1 || 1;
        document.getElementById(`check-${optId}`).checked = true;
      });
    });
  });
</script>