<div class="text-white">
  <span>
    <%= render AdminPageBannerComponent.new(title: @question[:question]) %>
  </span>
  <%== @question[:description] %>
  <form>
    <div class="text-black my-4">
      <textarea id="answer" name="<%= @question.questionnaire_options[0][:id] %>" class="w-full p-4 rounded-lg" placeholder="<%= @question[:question] %>" rows="20"><%= @answers[0][:answer_value] if @answers.present? %></textarea>
    </div>
    <div class="flex justify-between">
      <button type="button" class="btn-primary flex gap-x-8" id="backBtn" onclick="prevQuestionnaireStep()">
        <i class="fa-solid fa-arrow-left my-auto"></i>
        <span>Back</span>
      </button>
      <button type="submit" class="btn-primary flex gap-x-8" id="nextBtn">
        <span>Next</span>
        <i class="fa-solid fa-arrow-right my-auto"></i>
      </button>
    </div>
  </form>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    document.querySelector('form').addEventListener('submit', function(event) {
      event.preventDefault();
      
      const answer = document.getElementById('answer');
      
      let formData = {[answer.name]: answer.value};

      fetch('/school/questionnaires/<%= @question[:id] %>/submit', {
        method: 'POST',
        headers: {'Content-Type': 'application/json',},
        body: JSON.stringify({formData})
      }).then(response => {
          if (response.redirected) {
              window.location.href = response.url;
          }
      }).catch(e => {
          console.error('An error occurred', e);
      });
    });

    ClassicEditor.replace( 'answer', {
        toolbar: 'Full',
        enterMode : ClassicEditor.ENTER_BR,
        shiftEnterMode: ClassicEditor.ENTER_P
    
    });
  });

  function prevQuestionnaireStep() {
    fetch('/school/questionnaires/<%= @question[:id] %>/back', {
      method: 'GET',
    }).then(response => {
        if (response.redirected) {
            window.location.href = response.url;
        }
    }).catch(e => {
        console.error('An error occurred', e);
    });
  }
</script>
