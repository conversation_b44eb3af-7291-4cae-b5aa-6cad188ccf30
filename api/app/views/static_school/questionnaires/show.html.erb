<div class="max-w-[1400px] p-6 md:px-24 mx-auto">
    <% if !@question.present? %>
        <% if @question_index < 1 %>
            <%= render AdminPageBannerComponent.new(title: "Let us know a bit about you") %>
            <%= form_with model: @user.becomes(User), url: user_details_school_questionnaire_path(@questionnaire), local: true, multipart: true do |form| %>
                <% if @user.errors.any? %>
                    <div class="form-errors">
                        <ul>
                            <% @user.errors.full_messages.each do |message| %>
                                <li><%= message %></li>
                            <% end %>
                        </ul>
                    </div>
                <% end %>

                <div class="flex-1 min-w-[300px]">
                    <%= render AdminFormSectionComponent.new(title: "Details") do %>
                        <div class="sm:col-span-2">
                            <%= form.label :gender %>
                            <%= form.select :gender, options_for_select([['Select a gender', nil], ['Male', 'male'], ['Female', 'female'], ['Other', 'other']], selected: @user.gender) %>
                        </div>

                        <div class="sm:col-span-2">
                            <%= form.label :ethnicity %>
                            <%= form.select :ethnicity, options_for_select([['Select an ethnicity', nil], ['White', 'white'], ['Mixed or Multiple ethnic groups', 'mixed_or_multiple_ethnic_groups'], ['Asian or Asian British', 'asian_or_asian_british'], ['Black, African, Caribbean or Black British', 'black_african_caribbean_or_black_british'], ['Other ethnic group', 'other']], selected: @user.ethnicity) %>
                        </div>

                        <div class="sm:col-span-2">
                            <%= form.label :dob, 'Date of Birth' %>
                            <%= form.date_field :dob %>
                        </div>
                    <% end %>
                </div>

                <div class="flex justify-end">
                    <%= form.submit 'Next', class: "btn-primary" %>
                </div>
            <% end %>
        <% else %>
            <div>
                <h2 class="text-3xl text-white mb-8">Thank you for completing the questionnaire!</h2>
                <%= link_to 'To Dashboard', end_school_questionnaire_path, class: 'btn-primary' %>
            </div>
        <% end %>
    <% else %>
        <% case @question.question_type %>
        <% when 'multiChoiceImage' %>
        <%= render "multi_choice_image_form" %>
        <% when 'eitherOr' %>
        <%= render "either_or_form" %>
        <% when 'selectQuestion' %>
        <%= render "select_question_form" %>
        <% when 'gridChoice' %>
        <%= render "grid_choice_form" %>
        <% when 'freeText' %>
        <%= render "free_text_form" %>
    <% end %>
    <% end %>
</div>