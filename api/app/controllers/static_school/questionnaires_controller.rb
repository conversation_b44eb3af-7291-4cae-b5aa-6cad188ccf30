module StaticSchool
  class StaticSchool::QuestionnairesController < StaticSchoolController
    before_action :set_questionnaire, only: [:show, :user_details, :end]

    # GET /school/questionnaires/:id
    def show
      if session[:questionnaire_id] != @questionnaire.id || !session[:questionnaire_session_id]
        session[:questionnaire_id] = @questionnaire.id
        session[:questionnaire_step_id] = 0
        session[:questionnaire_session_id] ||= [Time.now.strftime("%Y%m%d%H%M%S"), current_user&.id || "anon"].join("_")
      end

      @question_index = session[:questionnaire_step_id] || 0
      questions = @questionnaire.questionnaire_questions.order("weight asc")

      @user = current_user if @question_index == 0

      @question = questions[@question_index - 1] if @question_index > 0
      @answers = QuestionnaireAnswer.where(session_id: session[:questionnaire_session_id], questionnaire_question_id: @question.id) if @question.present?
    end

    # POST /school/questionnaires/:id/submit
    def submit
      question_id = params[:id]
      data = params[:formData]
      session_id = session[:questionnaire_session_id]

      @question = QuestionnaireQuestion.find(question_id)

      ApplicationRecord.transaction do
        QuestionnaireAnswer.where(session_id: session_id, questionnaire_question_id: question_id).delete_all

        if @question[:question_type] == 'freeText'
          data.each do |key, value|
            QuestionnaireAnswer.create(
              user_id: current_user.id,
              questionnaire_question_id: question_id,
              questionnaire_option_id: key,
              answer_value: value,
              session_id: session_id
            )
          end
        else
          questionnaire_answers = []

          data.each do |opt_id, count|
            answer_value = QuestionnaireOption.find(opt_id)[:option_data]["value"]
            count.times do
              questionnaire_answers << {
                user_id: current_user.id,
                questionnaire_question_id: question_id,
                questionnaire_option_id: opt_id,
                answer_value: answer_value,
                session_id: session_id,
                created_at: Time.current,
                updated_at: Time.current
              }
            end
          end

          QuestionnaireAnswer.insert_all(questionnaire_answers) unless questionnaire_answers.empty?
        end
      end

      next_index = @question.next_question_index + 1
      session[:questionnaire_step_id] = next_index
      redirect_to school_questionnaire_path(id: @question.questionnaire_id)
    end

    # GET /school/questionnaires/:id/back
    def back
      question = QuestionnaireQuestion.find(params[:id])
      prev_index = question.previous_question_index
      session[:questionnaire_step_id] = prev_index
      redirect_to school_questionnaire_path(id: question.questionnaire_id)
    end

    # PATCH /school/questionnaires/:id/user_details
    def user_details
      if current_user.update(user_params)
        session[:questionnaire_step_id] = 1
        redirect_to school_questionnaire_path(id: @questionnaire.id)
      else
        render :show
      end
    end

    # GET /school/questionnaires/:id/end
    def end
      session.delete(:questionnaire_id)
      session.delete(:questionnaire_session_id)
      session.delete(:questionnaire_step_id)

      @questionnaire.questionnaire_users.where(user_id: current_user.id).first_or_create

      redirect_to '/accounts/dashboard'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_questionnaire
      @questionnaire = Questionnaire.find(params[:id])
    end

    def user_params
      params.require(:user).permit(:gender, :ethnicity, :dob)
    end

  end
end