module StaticAdmin
  class StaticAdmin::QuestionnairesController < StaticAdminController
    before_action :set_questionnaire, only: [:show, :edit, :update, :destroy, :statistics, :duplicate, :questions]

    # GET /admin/questionnaires
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @questionnaires = Questionnaire.all
      if params[:query].present?
        @questionnaires = @questionnaires.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      @questionnaires = @questionnaires.order("#{sort} #{order}").paginate(page: params[:page], per_page: 25)
    end

    # GET /admin/questionnaires/new
    def new
      @questionnaire = Questionnaire.new
    end

    # POST /admin/questionnaires
    def create
      @questionnaire = Questionnaire.new(questionnaire_params)

      if @questionnaire.save
        redirect_to [:edit, :admin, @questionnaire], notice: 'Questionnaire was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/questionnaires/:id/edit
    def edit; end

    # GET /admin/questionnaires/:id/questions
    def questions; end

    # PATCH/PUT /admin/questionnaires/:id
    def update
      if @questionnaire.update(questionnaire_params.except(:question_order))
        if questionnaire_params[:question_order].present?
          questionnaire_params[:question_order].split(',').each_with_index do |question_id, i|
              QuestionnaireQuestion.find(question_id).update(weight: i + 1)
          end
        end

        # Redirect based on the current tab context
        if params[:from_questions_tab]
          redirect_to questions_admin_questionnaire_path(@questionnaire), notice: 'Question order was successfully updated.'
        else
          redirect_to edit_admin_questionnaire_path(@questionnaire), notice: 'Questionnaire was successfully updated.'
        end
      else
        render :edit
      end
    end

    # DELETE /admin/questionnaires/:id
    def destroy
      @questionnaire.destroy
      redirect_to admin_questionnaires_url, notice: 'Questionnaire was successfully deleted.'
    end

    # GET /admin/questionnaires/:id/statistics
    def statistics
      @statistics = []

      @questionnaire.questionnaire_questions.order(:weight).map do |question|
          @statistics << {
              question: question,
              options_with_data: question.options_with_data(params.permit(:startDate, :endDate)),
          }
      end

      render :statistics
    end

    # POST /admin/questionnaires/:id/duplicate
    def duplicate
      new_questionnaire = @questionnaire.duplicate
      if new_questionnaire
        redirect_to admin_questionnaires_path, notice: 'Questionnaire was successfully duplicated.'
      else
        render :index
      end
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_questionnaire
      @questionnaire = Questionnaire.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def questionnaire_params
      params.require(:questionnaire).permit(:name, :new_library_unit_id, :include_demographics_questions, :is_onboarding_questionnaire, :published, :question_order)
    end

  end
end