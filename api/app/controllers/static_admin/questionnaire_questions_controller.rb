module StaticAdmin
  class StaticAdmin::QuestionnaireQuestionsController < StaticAdminController
    include Fileboy
    before_action :set_questionnaire, only: [:new, :create, :edit, :update, :destroy, :statistics]
    before_action :set_question, only: [:edit, :update, :destroy, :statistics]
    before_action :set_career_tags, only: [:edit, :update]

    # GET /admin/questionnaires/:questionnaire_id/question/new
    def new
      @question = QuestionnaireQuestion.new
    end

    # POST /admin/questionnaires/:questionnaire_id/question
    def create
      @question = QuestionnaireQuestion.new(question_params)
      @question.questionnaire = @questionnaire
      @question.weight = @questionnaire.questionnaire_questions.count

      if @question.save
        redirect_to edit_admin_questionnaire_question_path(@questionnaire, @question), notice: 'Question was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/questionnaires/:questionnaire_id/question/:id/edit
    def edit; end

    # PATCH/PUT /admin/questionnaires/:questionnaire_id/question/:id
    def update
      params["questionnaire_question"]["questionnaire_options_attributes"].each do |key, option|
        if option["fileboy_image_id"].present?
          option_data = option["option_data"] || {}

          fileboy_image_id = upload_image option["fileboy_image_id"]
          option_data["fileboyId"] = fileboy_image_id if fileboy_image_id

          question_params["questionnaire_options_attributes"][key]["option_data"] = option_data
        elsif option["id"].present?
          existing_option_data = @question.questionnaire_options.find(option["id"]).option_data
          new_option_data = option["option_data"].to_enum.to_h || {}
          existing_option_data = existing_option_data.is_a?(Hash) ? existing_option_data : JSON.parse(existing_option_data || '{}')

          params["questionnaire_question"]["questionnaire_options_attributes"][key]["option_data"] = existing_option_data.merge(new_option_data)
        else
          params["questionnaire_question"]["questionnaire_options_attributes"][key]["weight"] = key
        end
      end

      if @question.update(question_params)
        redirect_to edit_admin_questionnaire_question_path(@questionnaire, @question), notice: 'Question was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/questionnaires/:questionnaire_id/question/:id
    def destroy
      @question.destroy
      redirect_to edit_admin_questionnaire_path(@questionnaire), notice: 'Question was successfully deleted.'
    end

    # GET /admin/questionnaires/:questionnaire_id/question/:id/statistics
    def statistics
      param_data = params.permit(:ethnicity, :age_range_start, :age_range_end, :gender, :county, :date_from, :date_to)

      sql_query = <<-SQL
        SELECT
          (SELECT row_to_json(questionnaire_answers.*)) as answer,
          COALESCE(users.gender, 'not specified') as gender,
          (SELECT CASE
            WHEN users.ethnicity = 0 THEN 'white'
            WHEN users.ethnicity = 1 THEN 'mixed_or_multiple_ethnic_groups'
            WHEN users.ethnicity = 2 THEN 'asian_or_asian_british'
            WHEN users.ethnicity = 3 THEN 'black_african_caribbean_or_black_british'
            WHEN users.ethnicity = 4 THEN 'other'
            WHEN users.ethnicity IS NULL THEN 'not specified'
          END) as ethnicity,
          users.dob as date_of_birth,
          users.id as user_id,
          COALESCE(uk_schools.county, 'Not specified') as county
        FROM questionnaire_answers
        LEFT JOIN users ON questionnaire_answers.user_id = users.id AND NOT users.deleted
        LEFT JOIN schools ON users.school_id = schools.id AND NOT schools.deleted
        LEFT JOIN uk_schools ON schools.uk_school_id = uk_schools.id
        WHERE questionnaire_answers.questionnaire_question_id = :question_id
        AND CASE WHEN :from_date IS NOT NULL THEN questionnaire_answers.created_at >= :from_date ELSE TRUE END
        AND CASE WHEN :to_date IS NOT NULL THEN questionnaire_answers.created_at <= :to_date ELSE TRUE END
        AND CASE WHEN :ethnicity IS NOT NULL THEN users.ethnicity = :ethnicity ELSE TRUE END
        AND CASE WHEN :gender IS NOT NULL THEN users.gender = :gender ELSE TRUE END
        AND CASE WHEN :age_range_start IS NOT NULL THEN users.dob <= :age_range_start ELSE TRUE END
        AND CASE WHEN :age_range_end IS NOT NULL THEN users.dob >= :age_range_end ELSE TRUE END
        AND CASE WHEN :county IS NOT NULL THEN uk_schools.county = :county ELSE TRUE END
      SQL

      sql_query_params = {
        question_id: params[:id].to_i,
        from_date: param_data[:date_from].presence,
        to_date: param_data[:date_to].presence,
        ethnicity: param_data[:ethnicity].presence,
        gender: param_data[:gender].presence,
        age_range_start: param_data[:age_range_start].present? ? Date.today.prev_year(param_data[:age_range_start].to_i) : nil,
        age_range_end: param_data[:age_range_end].present? ? Date.today.prev_year(param_data[:age_range_end].to_i) : nil,
        county: param_data[:county].presence
      }

      @data = ActiveRecord::Base.connection.select_all(ActiveRecord::Base.sanitize_sql_array([sql_query, sql_query_params]))

      @genders = User.pluck("gender").uniq.compact.reject { |e| e == '0' }.map do |e|
        e.empty? ? ['Not Specified', ''] : [e.titleize, e]
      end
      @ethnicities = User.ethnicities.map { |k, v| [k.titleize, v] }
      @counties = UkSchool.order('county asc').pluck("county").uniq.compact.map { |e| [e.titleize, e] }

      answers = @question.questionnaire_answers.left_joins(user: { school: :uk_school })
      @counties_data = answers.pluck("uk_schools.county").uniq.compact.sort + ['Not specified']

      render :statistics
    end      

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_questionnaire
      @questionnaire = Questionnaire.find(params[:questionnaire_id])
    end

    def set_question
      @question = QuestionnaireQuestion.find(params[:id])
      @question.questionnaire_options = @question.questionnaire_options.order('weight asc')
    end

    def set_career_tags
      @career_tags = CareerTag.pluck(:name, :id)
    end

    # Only allow a list of trusted parameters through.
    def question_params
      params.require(:questionnaire_question).permit(:question, :description, :question_type, questionnaire_options_attributes: [:id, :weight, :_destroy, { option_data: {} }, career_tag_ids: []])
    end

  end
end